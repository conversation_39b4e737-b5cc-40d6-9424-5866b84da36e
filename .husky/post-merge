#!/usr/bin/env sh
# Stricter error handling: exits immediately if a command exits with a non-zero status.
# Be cautious with this if some commands are expected to fail and handled by || or if.
# set -e

. "$(dirname -- "$0")/_/husky.sh"

# Emojis
EMOJI_CLEAN="🧹"
EMOJI_INSTALL="📦"
EMOJI_SUCCESS="✅"
EMOJI_WARN="⚠️"
EMOJI_MERGE="🔀"
EMOJI_DEV="🚀"

# Detect OS
OS_RAW="$(uname)"
OS_LOWER="$(echo "$OS_RAW" | tr '[:upper:]' '[:lower:]')"

echo "$EMOJI_MERGE Post-merge hook started. OS detected: $OS_RAW"

# Check if yarn.lock or package.json changed in the merge commit
# Using HEAD@{1} as ORIG_HEAD might not always be set in post-merge depending on Git version/operation
# A more robust way to get the previous commit if ORIG_HEAD is problematic:
# prev_head=$(git rev-parse HEAD@{1})
# if [ $? -ne 0 ]; then
#   echo "$EMOJI_WARN Could not determine previous HEAD, assuming changes requiring install."
#   changed_files="yarn.lock" # Force install if unsure
# else
#   changed_files=$(git diff-tree -r --name-only --no-commit-id $prev_head HEAD | grep -E 'yarn.lock|package.json')
# fi
# Sticking to ORIG_HEAD for now as it's common, but be aware.
changed_files=$(git diff-tree -r --name-only --no-commit-id ORIG_HEAD HEAD | grep -E 'yarn.lock|package.json')

if [ -n "$changed_files" ]; then
  echo "$EMOJI_MERGE Detected changes in relevant files (yarn.lock or package.json):"
  echo "$changed_files"
  echo "Starting dependency cleanup and install..."

  # Remove node_modules safely
  if [ -d "node_modules" ]; then
    echo "$EMOJI_CLEAN Preparing to remove existing node_modules directory..."
    if [[ "$OS_LOWER" == "linux"* ]] || [[ "$OS_LOWER" == "darwin"* ]]; then
      echo "System type: Unix-like ($OS_LOWER)"
      if command -v lsof >/dev/null 2>&1; then
        # Ensure path is correct, assuming hook runs at repo root
        locked_pids=$(lsof +D "./node_modules" 2>/dev/null | awk 'NR>1 {print $2}' | sort -u)
        if [ -n "$locked_pids" ]; then
          echo "$EMOJI_WARN node_modules is in use by processes: $locked_pids. Attempting to kill..."
          for pid in $locked_pids; do
            if kill -9 "$pid" 2>/dev/null; then
              echo "$EMOJI_CLEAN Killed process $pid using node_modules."
            else
              echo "$EMOJI_WARN Failed to kill process $pid (it may have already exited)."
            fi
          done
          sleep 2 # Give OS time to release file locks
        fi
      else
        echo "$EMOJI_WARN lsof not found, cannot check for locked node_modules. Proceeding with removal."
      fi
      if rm -rf node_modules; then
        echo "$EMOJI_CLEAN node_modules removed."
      else
        echo "$EMOJI_WARN Failed to remove node_modules on Unix-like system. Check permissions or locked files."
        exit 1
      fi
    elif [[ "$OS_LOWER" == "msys"* ]] || [[ "$OS_LOWER" == "mingw"* ]] || [[ "$OS_LOWER" == "cygwin"* ]]; then
      echo "System type: Windows-like ($OS_LOWER via $OS_RAW)"
      echo "$EMOJI_WARN On Windows, ensure no processes (IDE, terminals) are locking node_modules."
      # Attempting rm -rf first, as it's common in Git Bash (MinGW)
      if rm -rf node_modules 2>/dev/null; then
        echo "$EMOJI_CLEAN node_modules removed using 'rm -rf'."
      elif cmd //c "rmdir /s /q node_modules" 2>/dev/null; then # Fallback to native Windows rmdir
        echo "$EMOJI_CLEAN node_modules removed using 'rmdir /s /q'."
      else
        # Check if directory still exists
        if [ -d "node_modules" ]; then
          echo "$EMOJI_WARN Failed to remove node_modules. It might be locked. Please close any processes using it and run 'yarn install' manually."
          exit 1
        else
          # This case means commands failed but directory is gone, which is unusual but possible.
          echo "$EMOJI_CLEAN node_modules directory is gone, though removal commands had issues."
        fi
      fi
      # Final verification
      if [ -d "node_modules" ]; then
          echo "$EMOJI_WARN CRITICAL: node_modules directory still exists after attempted removal."
          exit 1
      fi
    else
      echo "$EMOJI_WARN Unknown OS: $OS_RAW. Attempting Unix-style removal (rm -rf)."
      if rm -rf node_modules; then
        echo "$EMOJI_CLEAN node_modules removed."
      else
        echo "$EMOJI_WARN Failed to remove node_modules on Unknown OS. Check permissions or locked files."
        exit 1
      fi
    fi
  else
    echo "$EMOJI_CLEAN node_modules directory does not exist. Skipping removal."
  fi

  echo "$EMOJI_CLEAN Clearing Yarn cache..."
  if CI=true yarn cache clean </dev/null; then
    echo "$EMOJI_CLEAN Yarn cache cleaned."
  else
    echo "$EMOJI_WARN Failed to clean yarn cache."
    exit 1
  fi

  echo "$EMOJI_INSTALL Installing dependencies with frozen lockfile..."
  # Using CI=true to promote non-interactive behavior, </dev/null as an extra measure for stdin
  if CI=true yarn install --frozen-lockfile </dev/null; then
    echo "$EMOJI_INSTALL Dependencies installed successfully."
  else
    echo "$EMOJI_WARN Failed to install dependencies."
    exit 1
  fi

  echo "$EMOJI_DEV Starting 'yarn dev' for a brief verification..."
  # Using CI=true and redirecting stdin/stdout/stderr for yarn dev if it's noisy or problematic
  # Create a log file for yarn dev output to inspect if needed
  DEV_LOG_FILE="yarn_dev_hook_output.log"
  CI=true yarn dev </dev/null >"$DEV_LOG_FILE" 2>&1 &
  dev_pid=$!
  echo "$EMOJI_DEV 'yarn dev' started (pid $dev_pid), output logged to $DEV_LOG_FILE. Waiting 10 seconds..."

  sleep 10

  echo "$EMOJI_DEV Stopping 'yarn dev' (pid $dev_pid)..."
  # Try to kill the process group if possible, then the process itself
  if kill -0 $dev_pid 2>/dev/null; then # Check if process exists before trying to kill
    if ! kill -- -$dev_pid 2>/dev/null && ! kill $dev_pid 2>/dev/null && ! taskkill //PID $dev_pid //F //T 2>/dev/null; then
        echo "$EMOJI_WARN Failed to stop 'yarn dev' (pid $dev_pid) using available methods. It may need to be stopped manually."
    else
        echo "$EMOJI_DEV 'yarn dev' (pid $dev_pid) stop signal sent."
    fi
  else
    echo "$EMOJI_WARN 'yarn dev' (pid $dev_pid) process was not found or already exited. Check $DEV_LOG_FILE for details."
  fi
  # Brief pause to allow process termination
  sleep 2

  echo "$EMOJI_SUCCESS Dependency reinstallation and dev server check (brief run) complete."
else
  echo "$EMOJI_MERGE No changes in yarn.lock or package.json. No action needed."
fi

exit 0