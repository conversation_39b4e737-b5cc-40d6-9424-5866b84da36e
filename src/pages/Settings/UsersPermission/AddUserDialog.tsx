import React, { useEffect, useState, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { AttributeItem } from "../../../components/atoms/AttributeItem";
import { getAttributeTitleWidth } from "../../../utils";
import { styled } from "@linaria/react";
import { useTranslation } from "react-i18next";
import { useNotification } from "../../../utils/functions/customHooks";
import { IAttributes } from "../../../interfaces";
import { Dialog } from "primereact/dialog";
import { useMutation } from "react-query";
import { addPermissionUser } from "../../../services/node";

enum PermissionUser {
  PERSON_NAME = "personName",
  EMAIL = "email",
  PASSWORD = "password",
}

interface AddUserDialogProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: (newReaderId: number) => void;
}

const AddUserDialog: React.FC<AddUserDialogProps> = ({
  visible,
  onCancel,
  onSuccess,
}) => {
  const { t } = useTranslation();

  const { contextHolder, showErrorNotification } = useNotification();
  const [attributes, setAttributes] = useState<
    Array<Partial<IAttributes & { propId: PermissionUser }>>
  >([
    {
      name: t("Name"),
      value: "",
      type: "text",
      mandatory: true,
      propId: PermissionUser.PERSON_NAME,
    },
    {
      name: t("Email"),
      value: "",
      type: "text",
      mandatory: true,
      propId: PermissionUser.EMAIL,
    },
    {
      name: t("Password"),
      value: "",
      type: "password",
      mandatory: true,
      propId: PermissionUser.PASSWORD,
    },
  ]);
  const [editingAttribute, setEditingAttribute] = useState<string | null>(null);
  const [disabled, setDisabled] = useState(false);
  const [disabledInfo, setDisabledInfo] = useState("");

  const [focusedElementId, setFocusedElementId] = useState<string | null>(null);

  const dialogRef = useRef<Dialog>(null);
  const addButtonRef = useRef<HTMLButtonElement & HTMLAnchorElement>(null);
  const closeButtonRef = useRef<HTMLButtonElement>(null);

  const addUserMutation = useMutation<unknown>(addPermissionUser, {
    onSuccess: (newReaderId: number) => {
      onSuccess(newReaderId);
    },
    onError: () => {
      showErrorNotification("Please try again after sometimes");
    },
  });

  useEffect(() => {
    let hasError = false;
    let errorMsg = "";
    attributes.forEach((attr) => {
      if (
        attr.mandatory &&
        (!attr.value || attr.value.toString().trim() === "")
      ) {
        hasError = true;
        errorMsg = t("Please fill all mandatory fields");
      }
    });
    setDisabled(hasError);
    setDisabledInfo(errorMsg);
  }, [attributes, t]);

  const handleEdit = (name: string, value: any) => {
    setAttributes((attrs) =>
      attrs.map((attr) => (attr.name === name ? { ...attr, value } : attr))
    );
  };

  const handleAdd = () => {
    const payload = attributes.reduce((p, el) => {
      p[el.propId] = el.value;
      return p;
    }, {});
    addUserMutation.mutate(payload as any);
  };

  const calculateLabelWidths = () => {
    const dialogElement = dialogRef.current?.getElement();
    if (!dialogElement) return;
    const titles = dialogElement.querySelectorAll<HTMLElement>(
      ".comment-attribute-title"
    );
    titles.forEach((title) => {
      title.style.width = "fit-content";
    });
    const maxTitleWidth = getAttributeTitleWidth(".comment-attribute-title");
    titles.forEach((title) => {
      title.style.width = `${maxTitleWidth}px`;
    });

    const closeButton = dialogElement.querySelector<HTMLButtonElement>(
      ".p-dialog-header-close"
    );
    if (closeButton) {
      (closeButtonRef as React.MutableRefObject<HTMLButtonElement>).current =
        closeButton;
    }
  };

  useEffect(() => {
    if (visible) {
      setTimeout(calculateLabelWidths, 300);
      if (attributes.length > 0) {
        setFocusedElementId(attributes[0].name);
      }
    } else {
      const resetValues = attributes.map((a) => ({ ...a, value: "" }));
      setAttributes(resetValues);
      setEditingAttribute(null);
      setFocusedElementId(null);
    }
  }, [visible]);

  // REVISED useEffect: This hook now uses a functional update to avoid stale state.
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Tab") {
        event.preventDefault();

        // Use functional update form of setState. `currentFocusedId` is guaranteed to be the latest state.
        setFocusedElementId((currentFocusedId) => {
          const attributeNames = attributes.map((attr) => attr.name);
          const focusOrder = [...attributeNames, "addButton", "closeButton"];
          const tabbableElements = focusOrder.filter(
            (id) => id !== "addButton" || !disabled
          );

          const currentIndex = tabbableElements.indexOf(currentFocusedId);

          if (currentIndex === -1) {
            // Failsafe: if focus is somehow not in our cycle, reset to the first element.
            return tabbableElements[0];
          }

          const direction = event.shiftKey ? -1 : 1;
          const nextIndex =
            (currentIndex + direction + tabbableElements.length) %
            tabbableElements.length;
          return tabbableElements[nextIndex];
        });
      }
    };

    if (visible) {
      document.addEventListener("keyup", handleKeyDown);
    }
    return () => {
      document.removeEventListener("keyup", handleKeyDown);
    };
    // This hook's dependencies are now simpler and more correct.
  }, [visible, attributes, disabled]);

  // This hook correctly applies the focus based on the state. No changes needed here.
  useEffect(() => {
    if (!focusedElementId) return;

    const isAttribute = attributes.some(
      (attr) => attr.name === focusedElementId
    );

    if (isAttribute) {
      setEditingAttribute(focusedElementId);
    } else {
      setEditingAttribute(null);
      if (focusedElementId === "addButton") {
        addButtonRef.current?.focus();
      } else if (focusedElementId === "closeButton") {
        closeButtonRef.current?.focus();
      }
    }
  }, [focusedElementId]);

  return (
    <Dialog
      ref={dialogRef}
      header={t("Add Reader")}
      visible={visible}
      onHide={onCancel}
      style={{ width: "50vw", height: "auto" }}
      className="export-modal draggable-modal"
      onShow={() => setTimeout(calculateLabelWidths, 300)}
    >
      {/* Notification content place hodler */}
      {contextHolder}

      <Wrapper>
        <table
          style={{
            width: "100%",
            background: "#fff",
            borderRadius: 12,
            borderCollapse: "separate",
            borderSpacing: 0,
            marginTop: 0,
            marginBottom: 0,
            overflow: "hidden",
          }}
        >
          <tbody id="attributes-tbody">
            {attributes.map((attr) => (
              <AttributeItem
                key={attr.name}
                title={attr.name}
                value={attr.value}
                type={attr.type}
                onEdit={(val) => handleEdit(attr.name, val)}
                mandatory={attr.mandatory}
                isEditing={editingAttribute === attr.name}
                onEditClick={() =>
                  setFocusedElementId(
                    focusedElementId === attr.name ? null : attr.name
                  )
                }
                titleClassName={"comment-attribute-title"}
              />
            ))}
          </tbody>
        </table>
        <div
          className="buttons"
          style={{
            display: "flex",
            justifyContent: "flex-end",
            marginTop: 32,
          }}
        >
          <Tooltip title={disabledInfo} placement="bottomLeft">
            <Button
              ref={addButtonRef}
              type="primary"
              shape="round"
              className="primary-button"
              loading={addUserMutation.isLoading}
              onClick={handleAdd}
              disabled={disabled || addUserMutation.isLoading}
              style={{ minWidth: 100 }}
            >
              {t("Add")}
            </Button>
          </Tooltip>
        </div>
      </Wrapper>
    </Dialog>
  );
};

export default AddUserDialog;

const Wrapper = styled.div`
  padding-bottom: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;

  & .attribute-wrapper {
    flex: 1;
    display: flex;
    flex-wrap: nowrap;
    overflow: auto;
    flex-direction: column;
    justify-content: flex-start;
  }

  & button:disabled {
    background-color: var(--color-text);
    color: #fff;
    opacity: 0.7;
  }

  & .buttons {
    display: flex;
    justify-content: right;
    margin-top: 20px;

    & button {
      font-size: 13px;
      box-shadow: none;
    }
  }
`;
