import React, { useEffect, useState, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { AttributeItem } from "../../../components/atoms/AttributeItem";
import { getAttributeTitleWidth } from "../../../utils";
import { styled } from "@linaria/react";
import { useTranslation } from "react-i18next";
import { useNotification } from "../../../utils/functions/customHooks";
import { Dialog } from "primereact/dialog";
import { useMutation } from "react-query";
import { addNodeService } from "../../../services/node";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { withErrorBoundary } from "../../../components/withErrorBoundary";

/**
 * AddGroupDialog allows creating a new group node with dynamic attributes.
 * - Auto-focuses the first input on open.
 * - <PERSON>les tab navigation and label translation for static fields.
 * - Only includes the first editor-type attribute in the payload body.
 * - Robust against future dynamic attribute additions.
 */
const AddGroupDialog = ({
  visible,
  onCancel,
  onSuccess,
  templateId,
  parentId,
}) => {
  const { t } = useTranslation();
  const { contextHolder, showErrorNotification } = useNotification();
  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );
  const [attributes, setAttributes] = useState([]);
  const [editingAttribute, setEditingAttribute] = useState(null);
  const [disabled, setDisabled] = useState(false);
  const [disabledInfo, setDisabledInfo] = useState("");
  const [groupName, setGroupName] = useState("");
  const [focusedElementId, setFocusedElementId] = useState(null);
  const dialogRef = useRef(null);
  const addButtonRef = useRef(null);
  const closeButtonRef = useRef(null);

  // Refs for focus management
  const nameInputRef = useRef(null);
  const attributeInputRefs = useRef({});

  // Find template by templateId
  const template = templatesData ? templatesData[templateId] : null;

  useEffect(() => {
    if (template && template.attributeTemplates) {
      setAttributes(
        template.attributeTemplates.map((attr) => ({
          ...attr,
          value: attr.defaultValue?.value || "",
          touched: false,
          draft: true,
        }))
      );
      setGroupName("");
    }
  }, [templateId, template]);

  useEffect(() => {
    let hasError = false;
    let errorMsg = "";
    // Name is always required
    if (!groupName.trim()) {
      hasError = true;
      errorMsg = t("Please fill all mandatory fields");
    }
    // Only validate non-select attributes
    attributes.forEach((attr) => {
      if (
        attr.type !== "select" &&
        attr.mandatory &&
        (!attr.value || attr.value.toString().trim() === "")
      ) {
        hasError = true;
        errorMsg = t("Please fill all mandatory fields");
      }
    });
    setDisabled(hasError);
    setDisabledInfo(errorMsg);
  }, [attributes, groupName, t]);

  const handleEdit = (name, value) => {
    if (name === "Name") {
      setGroupName(value);
    } else {
      setAttributes((attrs) =>
        attrs.map((attr) =>
          attr.name === name ? { ...attr, value, touched: true } : attr
        )
      );
    }
  };

  const addGroupMutation = useMutation(addNodeService, {
    onSuccess: () => {
      onSuccess();
    },
    onError: () => {
      showErrorNotification(t("Please try again after sometimes"));
    },
  });

  const handleAdd = () => {
    if (!template || typeof parentId !== "number") {
      showErrorNotification(t("Template or parentId missing"));
      return;
    }
    const body = [];

    const editorAttr = attributes.find((attr) => attr.type === "editor");
    if (editorAttr) {
      body.push({
        ...editorAttr,
        touched: true,
        draft: true,
      });
    }

    // Always add select-type attributes with the first item as value
    attributes.forEach((attr) => {
      if (attr.type === "select" && attr.items) {
        const [firstValue, firstLabel] = Object.entries(attr.items)[0] || [];
        if (firstValue) {
          body.push({
            value: { [firstValue]: firstLabel },
            id: attr.id,
            bitFlag: attr.bitFlag,
            mandatory: attr.mandatory,
            name: attr.name,
            type: attr.type,
            defaultValue: attr.defaultValue,
          });
        }
      }
    });

    // Add other types as needed (e.g., "source" if you have a special case)

    const payload = {
      name: groupName,
      nodeType: template.nodeType,
      templateId: Number(template.id),
      body,
      id: parentId,
    };
    addGroupMutation.mutate(payload);
  };

  const calculateLabelWidths = () => {
    const dialogElement = dialogRef.current?.getElement?.();
    if (!dialogElement) return;
    const titles = dialogElement.querySelectorAll(".comment-attribute-title");
    titles.forEach((title) => {
      title.style.width = "fit-content";
    });
    const maxTitleWidth = getAttributeTitleWidth(".comment-attribute-title");
    titles.forEach((title) => {
      title.style.width = `${maxTitleWidth}px`;
    });
    const closeButton = dialogElement.querySelector(".p-dialog-header-close");
    if (closeButton) {
      closeButtonRef.current = closeButton;
    }
  };

  // Auto-focus the first input after rendering
  useEffect(() => {
    if (visible) {
      setFocusedElementId("Name");
      setEditingAttribute("Name");
      setTimeout(() => {
        nameInputRef.current?.focus();
        calculateLabelWidths();
      }, 100);
    } else {
      setAttributes((attrs) => attrs.map((a) => ({ ...a, value: "" })));
      setEditingAttribute(null);
      setFocusedElementId(null);
      setGroupName("");
    }
  }, [visible]);

  // Tab navigation logic (matches AddUserDialog)
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Tab") {
        event.preventDefault();
        setFocusedElementId((currentFocusedId) => {
          const attributeNames = attributes.map((attr) => attr.name);
          const focusOrder = [
            "Name",
            ...attributeNames,
            "addButton",
            "closeButton",
          ];
          const tabbableElements = focusOrder.filter(
            (id) => id !== "addButton" || !disabled
          );
          let currentIndex = tabbableElements.indexOf(currentFocusedId);
          if (currentIndex === -1) currentIndex = 0;
          const direction = event.shiftKey ? -1 : 1;
          let nextIndex = currentIndex + direction;
          if (nextIndex < 0) nextIndex = tabbableElements.length - 1;
          if (nextIndex >= tabbableElements.length) nextIndex = 0;
          const nextId = tabbableElements[nextIndex];
          return nextId;
        });
      }
    };
    if (visible) {
      document.addEventListener("keyup", handleKeyDown);
    }
    return () => {
      document.removeEventListener("keyup", handleKeyDown);
    };
  }, [visible, attributes, disabled]);

  useEffect(() => {
    if (!focusedElementId) return;

    const attributeNames = attributes.map((attr) => attr.name);
    const isAttribute = [...attributeNames, "Name"].includes(focusedElementId);

    if (isAttribute) {
      setEditingAttribute(
        focusedElementId === "Name" ? null : focusedElementId
      );
      if (focusedElementId === "Name") {
        setTimeout(() => nameInputRef.current?.focus(), 0);
      } else {
        setTimeout(
          () => attributeInputRefs.current[focusedElementId]?.focus(),
          0
        );
      }
    } else {
      setEditingAttribute(null);
      if (focusedElementId === "addButton") {
        setTimeout(() => addButtonRef.current?.focus(), 0);
      } else if (focusedElementId === "closeButton") {
        setTimeout(() => closeButtonRef.current?.focus(), 0);
      }
    }
  }, [focusedElementId]);

  // Filter out select attributes for UI
  const mappedAttributes = attributes
    .filter((attr) => attr.type !== "select")
    .map((attr) => {
      if (attr.type === "select" && attr.items) {
        return {
          ...attr,
          dropdownItems: Object.entries(attr.items).map(([value, label]) => ({
            value,
            label,
          })),
        };
      }
      return attr;
    })
    .reverse();

  const finalAttributes = [
    {
      name: "Name",
      value: groupName,
      type: "text",
      mandatory: true,
      isNameField: true,
      id: "__name__",
    },
    ...mappedAttributes,
  ];
  return (
    <Dialog
      ref={dialogRef}
      header={t("Add Group")}
      visible={visible}
      onHide={onCancel}
      style={{ width: "50vw", height: "auto" }}
      className="export-modal draggable-modal"
      onShow={() => setTimeout(calculateLabelWidths, 300)}
    >
      {contextHolder}
      <Wrapper>
        <table
          style={{
            width: "100%",
            background: "#fff",
            borderRadius: 12,
            borderCollapse: "separate",
            borderSpacing: 0,
            marginTop: 0,
            marginBottom: 0,
            overflow: "hidden",
          }}
        >
          <tbody id="attributes-tbody">
            {finalAttributes.map((attr) => (
              <AttributeItem
                key={attr.name}
                title={attr.isNameField ? t(attr.name) : attr.name}
                value={attr.value}
                type={attr.type}
                onEdit={(val) => handleEdit(attr.name, val)}
                mandatory={attr.mandatory}
                isEditing={
                  attr.name === "Name"
                    ? focusedElementId === "Name"
                    : editingAttribute === attr.name
                }
                onEditClick={() =>
                  setFocusedElementId(
                    focusedElementId === attr.name ? null : attr.name
                  )
                }
                titleClassName={"comment-attribute-title"}
                id={attr.id}
                dropdownItems={attr.dropdownItems}
              />
            ))}
          </tbody>
        </table>
        <div
          className="buttons"
          style={{
            display: "flex",
            justifyContent: "flex-end",
            marginTop: 32,
          }}
        >
          <Tooltip title={disabledInfo} placement="bottomLeft">
            <Button
              ref={addButtonRef}
              type="primary"
              shape="round"
              className="primary-button"
              loading={addGroupMutation.isLoading}
              onClick={handleAdd}
              disabled={disabled || addGroupMutation.isLoading}
              style={{ minWidth: 100 }}
              id="addButton"
            >
              {t("Add")}
            </Button>
          </Tooltip>
        </div>
      </Wrapper>
    </Dialog>
  );
};

export default withErrorBoundary(AddGroupDialog, "error.generic");

const Wrapper = styled.div`
  padding-bottom: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  & .attribute-wrapper {
    flex: 1;
    display: flex;
    flex-wrap: nowrap;
    overflow: auto;
    flex-direction: column;
    justify-content: flex-start;
  }
  & button:disabled {
    background-color: var(--color-text);
    color: #fff;
    opacity: 0.7;
  }
  & .buttons {
    display: flex;
    justify-content: right;
    margin-top: 20px;
    & button {
      font-size: 13px;
      box-shadow: none;
    }
  }
`;
