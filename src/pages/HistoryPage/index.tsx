import {
  LoadingOutlined,
  NodeCollapseOutlined,
  NodeExpandOutlined,
} from "@ant-design/icons";
import { <PERSON><PERSON>, <PERSON>lex, Tooltip } from "antd";
import { transformObjectPath, useTheme } from "../../utils";
import { Suspense, useEffect, useState } from "react";
import { Wrapper } from "./style";
import { BreadCrumb, MyTable } from "../../components";
import { GET_LOCAL_SETTINGS_KEY } from "../../constants";
import { useTranslation } from "react-i18next";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
  useHyperlinkActions,
  useNotification,
  useParentHeight,
  useTemplateActions,
} from "../../utils/functions/customHooks";
import { ILocalSettings } from "../../interfaces";
import { saveLocalSettings } from "../../services";
import { getAllNodesHistory } from "../../services/history";
import { useDispatch, useSelector } from "react-redux";
import {
  setBreadcrumb,
  setMask,
  setParentBreadcrumbs,
  setHideAllPathNames,
} from "../../store/features";
import { RootState } from "../../store";
import dayjs from "dayjs";
import { Dialog } from "primereact/dialog";
import { styled } from "@linaria/react";
import { withErrorBoundary } from "../../components/withErrorBoundary";

const DeletedDialogWrapper = styled.div`
  padding-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  & .buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
    width: 100%;
  }
`;

const HistoryPage = () => {
  const theme = useTheme();
  const groupStyles = (isVisible: boolean) => {
    return {
      width: 25,
      height: 20,
      padding: 0,
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      background: isVisible ? "#fff" : "transparent",
      color: isVisible ? theme.colorPrimary : "#fff",
    };
  };
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const { handleHyperlinkAction } = useHyperlinkActions();
  const { getTemplateName, getTemplateType } = useTemplateActions();
  const { height, ref: parentRef } = useParentHeight();

  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();
  const [resetTrigger, setResetTrigger] = useState(0);
  const [allColumnsRequest, setAllColumnsRequest] = useState([]);
  const [deletedDialogVisible, setDeletedDialogVisible] = useState(false);

  // Fetch history data
  const { data: historyData = [], isLoading } = useQuery(
    ["allNodesHistory"],
    getAllNodesHistory
  );

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  useEffect(() => {
    dispatch(setBreadcrumb([]));
    dispatch(setParentBreadcrumbs([]));
  }, [dispatch]);

  useEffect(() => {
    if (!localSettingsData) return;
    if (localSettingsData?.body[0]?.value?.historyTable?.columns) {
      const allColumns = [];
      localSettingsData.body[0].value.historyTable.columns?.forEach(
        (column) => {
          const index = columns.findIndex((item) => item.field === column);
          if (index !== -1) allColumns.push({ ...columns[index] });
        }
      );
      setAllColumnsRequest(allColumns);
    } else {
      setAllColumnsRequest(columns);
    }
  }, [localSettingsData]);

  const detectChange = () => {
    if (!mask) {
      dispatch(setMask(true));
    }
  };

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      dispatch(setMask(false));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setMask(false));
    },
  });

  const handleSave = () => {
    const request = {
      value: {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        historyTable: {
          columns: allColumnsRequest.map((col) => col.field),
        },
      },
    };
    mutation.mutate(request);
  };

  const mask = useSelector((state: RootState) => state.sidebar.mask);
  const hideAllPathNames = useSelector(
    (state: RootState) => state.breadcrumbs.hideAllpathNames
  );

  const handleCancel = () => {
    dispatch(setMask(false));
    setResetTrigger((trigger) => trigger + 1);
  };

  // Columns as per requirements, using real API data structure
  const columns = [
    {
      headerName: t("Date & time"),
      field: "timestamp",
      width: 180,
      cellRenderer: ({ data }) =>
        dayjs(data.timestamp).format("YYYY-MM-DD HH:mm"),
    },
    {
      headerName: t("Operation"),
      field: "message",
      minWidth: 200,
      flex: 1,
      cellRenderer: ({ data }) => t(data.message),
    },
    {
      headerName: t("Asset type"),
      field: "nodeTemplateId",
      width: 160,
      cellRenderer: ({ data }) => getTemplateName(data.nodeTemplateId) || "-",
    },
    {
      headerName: t("Asset"),
      field: "nodeName",
      width: 220,
      cellRenderer: ({ data }) => {
        return (
          <LinkWrapper theme={theme}>
            <a
              className="clickable-link"
              style={{
                color: data.nodeInTrash
                  ? theme["trashBreadcrumbsColor"]
                  : data.operation === "OP_DELETE_NODE"
                  ? theme.colorTextTertiary
                  : theme.colorPrimary,
              }}
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                if (data.operation === "OP_DELETE_NODE") {
                  setDeletedDialogVisible(true);
                } else {
                  handleHyperlinkAction({
                    id: data.nodeId,
                    inTrash: data.nodeInTrash,
                  });
                }
              }}
            >
              {data.nodeName}
            </a>
          </LinkWrapper>
        );
      },
    },
    ...(!hideAllPathNames
      ? [
          {
            headerName: t("Path"),
            field: "nodePathName",
            width: 300,
            cellRenderer: ({ data }) =>
              transformObjectPath(data.nodePathName, data.nodeInTrash) || "-",
          },
        ]
      : []),
  ];

  // Filter out metamodel operations before sorting and binding to table
  const filteredHistoryData = (historyData || []).filter(
    (data) => getTemplateType(data["nodeTemplateId"]) !== "META"
  );
  // Sort by most recent
  const sortedHistoryData = [...filteredHistoryData].sort((a, b) => {
    const aTime = dayjs(a.timestamp).valueOf();
    const bTime = dayjs(b.timestamp).valueOf();
    return bTime - aTime;
  });

  return (
    <Suspense fallback={<LoadingOutlined />}>
      <Wrapper theme={theme}>
        {contextHolder}
        <BreadCrumb
          extra={
            <Flex gap={10} align="center">
              <Tooltip
                title={hideAllPathNames ? t("Show Path") : t("Hide Path")}
                placement="bottom"
              >
                <Button
                  type="text"
                  disabled={mask}
                  icon={
                    !hideAllPathNames ? (
                      <NodeCollapseOutlined />
                    ) : (
                      <NodeExpandOutlined />
                    )
                  }
                  onClick={() =>
                    dispatch(setHideAllPathNames(!hideAllPathNames))
                  }
                  style={{
                    ...groupStyles(hideAllPathNames),
                  }}
                />
              </Tooltip>
              {mask && (
                <>
                  <Button
                    className="breadcrumb-button cancel-button"
                    type="primary"
                    onClick={handleCancel}
                  >
                    {t("Cancel")}
                  </Button>
                  <Button
                    className="breadcrumb-button save-button"
                    type="primary"
                    onClick={handleSave}
                    loading={mutation.isLoading}
                  >
                    {t("Save")}
                  </Button>
                </>
              )}
            </Flex>
          }
        />
        <div
          className="content"
          ref={parentRef}
          style={{ height: "100%", position: "relative" }}
        >
          <MyTable
            loading={isLoading}
            emptyMessage={t("No history")}
            height={height - 110 + "px"}
            data={sortedHistoryData}
            resetTrigger={resetTrigger}
            columns={columns}
            setColumnsRequest={setAllColumnsRequest}
            detectChange={detectChange}
            excelFileName="history"
          />
        </div>
        <Dialog
          header={t("Info")}
          visible={deletedDialogVisible}
          onHide={() => setDeletedDialogVisible(false)}
          style={{ width: 400 }}
          className="export-modal draggable-modal"
          footer={null}
        >
          <DeletedDialogWrapper>
            <p
              style={{
                margin: "20px auto",
                textAlign: "center",
                fontSize: 13,
              }}
            >
              {t("Object permanently deleted")}
            </p>
            <div className="buttons">
              <Button onClick={() => setDeletedDialogVisible(false)}>
                {t("OK")}
              </Button>
            </div>
          </DeletedDialogWrapper>
        </Dialog>
      </Wrapper>
    </Suspense>
  );
};

export default withErrorBoundary(HistoryPage, "error.generic");

const LinkWrapper = styled.div<{ theme: any }>`
  > a {
    text-decoration: none;
    font-size: 13px;
    cursor: pointer;
    color: ${({ theme }) => theme.colorTextDisabled};

    &:hover {
      text-decoration: underline;
    }
  }
`;
