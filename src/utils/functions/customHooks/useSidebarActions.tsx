import { useMutation, useQueryClient } from "react-query";
import { RootState } from "../../../store";
import { useDispatch, useSelector } from "react-redux";
import { ILocalSettings, ITreeData } from "../../../interfaces";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { searchRecursivelyByKey } from "../recursives";
import {
  DISABLED_METAMODEL_NODE_ID,
  GET_CHILDRENS,
  GET_LOCAL_SETTINGS_KEY,
  OBJECT_TEMPLATE_ID,
  TEMP_GROUPING_NODE_ID,
  getAttributeIcon,
} from "../../../constants";
import { setExpandedKeys, setSelected } from "../../../store/features/sidebar";
import { useTemplateActions } from "./useTemplateActions";
import { getAllNodes } from "../../../services/node";
import { setBreadcrumb, setRefreshBreadcrumbs } from "../../../store/features";
import { setPinned } from "../../../store/features/pinned";
import { saveLocalSettings } from "../../../services";
import { useFlags } from "./useFlags";
import { isEqual } from "lodash";

const useSidebarActions = () => {
  const { expandedKeys, selected } = useSelector(
    (state: RootState) => state.sidebar
  );

  const pinned = useSelector((state: RootState) => state.pinned.pinned);
  const selectedTrash = useSelector(
    (state: RootState) => state.trash.selectedTrash
  );

  const { getTemplateIcon, getTemplateName, getAllowedChildrens } =
    useTemplateActions();

  const { getFlags } = useFlags();

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const params = useParams();
  const parentMenuNodeId = params?.nodeId;
  const location = useLocation();
  const metamodel = location?.pathname?.includes("metamodel");
  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );

  const updateQueryClientChildRecursively = (data, id, name, action) => {
    const BreakException = {};
    try {
      if (action.id == "-1") {
        data.unshift({
          body: [],
          id: id,
          last: true,
          name: name,
          parentId: action.id,
          templateId: 0,
        });
      } else
        data.forEach((item) => {
          if (item.id === action.id) {
            item.last = false;
            item.children = [
              ...(item.children || []),
              {
                body: [],
                id: id,
                last: true,
                name: name,
                parentId: action.id,
                templateId: 0,
              },
            ];
            throw BreakException;
          } else if (item.children && item.children.length > 0) {
            updateQueryClientChildRecursively(item.children, id, name, action);
          }
        });
    } catch (e) {
      if (e !== BreakException) throw e;
    }
  };

  const renameBreadcrumbRecursively = (
    nodes: ITreeData[],
    newName: string,
    toUpdateNodeId: number
  ) => {
    nodes?.forEach((node) => {
      node.breadcrumb = node?.breadcrumb?.map((breadcrumbItem) =>
        breadcrumbItem.id === toUpdateNodeId
          ? { ...breadcrumbItem, title: newName }
          : breadcrumbItem
      );

      if (node.children && node.children.length > 0) {
        renameBreadcrumbRecursively(node.children, newName, toUpdateNodeId);
      }
    });
  };

  const renameRecursively = (
    data: ITreeData[],
    newName: string,
    id: number
  ) => {
    const findAndRename = (items) => {
      for (const item of items) {
        if (item.key === id) {
          item.name = newName;
          const newBreadcrumbs = item?.breadcrumb?.map(
            (breadcrumbItem, index) =>
              index === item.breadcrumb.length - 1
                ? { ...breadcrumbItem, title: newName }
                : breadcrumbItem
          );

          item.breadcrumb = newBreadcrumbs;
          if (newBreadcrumbs) dispatch(setBreadcrumb([...newBreadcrumbs]));

          if (item.children && item.children.length > 0) {
            renameBreadcrumbRecursively(item.children, newName, id);
          }
          return true;
        }

        if (item.children && item.children.length > 0) {
          if (findAndRename(item.children)) {
            return true; // Stop recursion if item was found and renamed
          }
        }
      }
      return false; // Item not found in this branch
    };

    findAndRename(data);
  };

  const updateNodeAfterPublish = (
    data: ITreeData[],
    nodeDetails: any,
    childrens: ITreeData[]
  ) => {
    const findAndRename = (items) => {
      for (const item of items) {
        if (item.key === nodeDetails?.id) {
          // Only update properties if they have changed, to preserve object identity
          let changed = false;
          if (item.flag !== nodeDetails?.flag) {
            item.flag = nodeDetails?.flag;
            changed = true;
          }
          if (item.name !== nodeDetails?.name) {
            item.name = nodeDetails?.name;
            changed = true;
          }
          // Breadcrumbs
          const newBreadcrumbs = item?.breadcrumb?.map(
            (breadcrumbItem, index) =>
              index === item.breadcrumb.length - 1
                ? { ...breadcrumbItem, title: nodeDetails?.name }
                : breadcrumbItem
          );
          if (!isEqual(item.breadcrumb, newBreadcrumbs)) {
            item.breadcrumb = newBreadcrumbs;
            dispatch(setBreadcrumb([...newBreadcrumbs]));
            changed = true;
          }
          // Children: preserve reference for unchanged children
          const prevChildren = item.children || [];
          const newChildren =
            childrens?.map((node: ITreeData) => {
              const breadcrumbItem = {
                ...node,
                parentId: node.parentId || 0,
                templateName: getTemplateName(node.templateId),
                allowedChildrens: getAllowedChildrens(node.templateId),
                isLeaf: node?.countChildren === 0,
              };
              const breadcrumb = [...newBreadcrumbs, breadcrumbItem];
              const prevChild = prevChildren.find((c) => c.key === node.id);
              // Compare key, name, flag, and isLeaf for shallow equality
              if (
                prevChild &&
                prevChild.name === node.name &&
                isEqual(prevChild.flag, node?.bitFlag) &&
                prevChild.isLeaf === (node?.countChildren === 0)
              ) {
                return prevChild;
              }
              return {
                key: node?.id,
                templateId: node.templateId,
                name: node.name,
                title: node.name,
                icon: getTemplateIcon(node.templateId),
                breadcrumb: breadcrumb,
                parentId: node.parentId,
                isLeaf: node?.countChildren === 0,
                children: [],
                flag: getFlags(node?.bitFlag),
              };
            }) || [];
          if (!isEqual(prevChildren, newChildren)) {
            item.children = newChildren;
            changed = true;
          }
          // isLeaf and countChildren
          const newIsLeaf = (item.children?.length ?? 0) === 0;
          if (item.isLeaf !== newIsLeaf) {
            item.isLeaf = newIsLeaf;
            changed = true;
          }
          item.countChildren = item.children?.length;
          return changed;
        }

        if (item.children && item.children.length > 0) {
          if (findAndRename(item.children)) {
            return true; // Stop recursion if item was found and renamed
          }
        }
      }
      return false; // Item not found in this branch
    };

    findAndRename(data);
    return data;
  };

  const updateNodeFromTrash = (data: ITreeData[], toUpdateNodeId: number) => {
    const BreakException = {};
    try {
      // if (action.fromHeader) {
      //   data.unshift({
      //     key: id,
      //     name: name,
      //     title: name,
      //     parentId: Number(action.id),
      //     templateId: action.templateId,
      //     isLeaf: true,
      //     children: null,
      //     allowedChildrens: [],
      //     breadcrumb: [
      //       {
      //         id: id,
      //         title: name,
      //         parentId: action.id,
      //         templateName: templateName,
      //         allowedChildrens: allowedChildrens,
      //       },
      //     ],
      //     icon: getAttributeIcon(icon),
      //   });
      // } else {
      data.forEach((item: ITreeData) => {
        if (item.key == toUpdateNodeId) {
          item.isLeaf = false;
          const newNodes = [];
          selectedTrash?.info?.forEach((selectedTrashNode) => {
            const selectedTemplate =
              templatesData[selectedTrashNode.templateId];
            newNodes.push({
              key: selectedTrashNode.id,
              loaded: false,
              name: selectedTrashNode.name,
              title: selectedTrashNode.name,
              parentId: toUpdateNodeId,
              templateId: selectedTrashNode.templateId,
              isLeaf: selectedTrashNode.isLeaf,
              breadcrumb: [
                ...item.breadcrumb,
                {
                  id: selectedTrashNode.id,
                  title: selectedTrashNode.name,
                  parentId: toUpdateNodeId,
                  templateName: selectedTemplate?.name,
                  templateId: selectedTemplate?.id,
                  allowedChildrens: selectedTemplate?.allowedChildren,
                },
              ],
              children: [],
              icon: getAttributeIcon(selectedTemplate?.icon),
            });
          });

          item.children = [
            ...newNodes,
            ...(item?.children ? item.children : []),
          ];

          throw BreakException;
        } else if (item.children && item.children.length > 0) {
          updateNodeFromTrash(item.children, toUpdateNodeId);
        }
      });
    } catch (e) {
      if (e !== BreakException) throw e;
    }
  };

  const addNewNodeRecursively = (
    data,
    id,
    name,
    icon,
    action,
    templateName,
    allowedChildrens
  ) => {
    const BreakException = {};
    try {
      if (action.fromHeader) {
        data.unshift({
          key: id,
          name: name,
          title: name,
          parentId: Number(action.id),
          templateId: Number(action.templateId),
          isLeaf: true,
          children: [],
          allowedChildren: allowedChildrens,
          flag:
            action.templateId == OBJECT_TEMPLATE_ID
              ? ["NEWTEMPLATE", "EDITMODE"]
              : [],
          breadcrumb: [
            {
              id: id,
              title: name,
              parentId: action.id,
              templateName: templateName,
              templateId: Number(action.templateId),
              allowedChildrens: allowedChildrens,
            },
          ],
          icon: getAttributeIcon(icon),
          body: [
            {
              id: "-1",
              type: "allowedChildren",
              value: allowedChildrens,
            },
          ],
        });
      } else {
        data.forEach((item) => {
          if (item.key == action.id) {
            item.isLeaf = false;

            item.children = [
              {
                key: id,
                name: name,
                title: templateName,
                flag:
                  action.templateId == OBJECT_TEMPLATE_ID
                    ? ["NEWTEMPLATE", "EDITMODE"]
                    : [],
                parentId: action.id,
                templateId: Number(action.templateId),
                isLeaf: true,
                children: [],
                allowedChildren: allowedChildrens,
                breadcrumb:
                  action?.templateId == "-15"
                    ? null
                    : [
                        ...item.breadcrumb,
                        {
                          id: id,
                          title: name,
                          parentId: action.id,
                          templateName: templateName,
                          templateId: Number(action.templateId),
                          allowedChildrens: allowedChildrens,
                        },
                      ],
                icon: getAttributeIcon(icon),
                body: [
                  {
                    id: "-1",
                    type: "allowedChildren",
                    value: allowedChildrens,
                  },
                ],
              },
              ...(item?.children ? item.children : []),
            ];
            item.countChildren = item.children.length;

            throw BreakException;
          } else if (item.children && item.children.length > 0) {
            addNewNodeRecursively(
              item.children,
              id,
              name,
              icon,
              action,
              templateName,
              allowedChildrens
            );
          }
        });
      }
    } catch (e) {
      if (e !== BreakException) throw e;
    }
  };

  const updateChildNode = async (
    treeData,
    id: number,
    name: string,
    icon,
    action,
    allowedChildren
  ) => {
    // RENAME STARTS

    if (action.key === "rename") {
      // rename
      const newTreeData = [...treeData];
      renameRecursively(newTreeData, name, action?.id);

      // dispatch(renameNode({ name: name, id: action?.id }));
      const selectedNode = searchRecursivelyByKey(treeData, action?.id, "key");
      // updating queryclient too
      if (selectedNode) {
        const previousData = queryClient.getQueryData([
          GET_CHILDRENS,
          selectedNode?.parentId.toString(),
        ]) as ITreeData[];
        if (previousData) {
          const index = previousData.findIndex(
            (item: ITreeData) => item?.id == selectedNode?.key
          );
          if (index !== -1) {
            previousData[index].name = name;
            queryClient.setQueryData(
              [GET_CHILDRENS, selectedNode?.parentId.toString()],
              previousData
            );
          }
        }
      }

      // RENAME ENDS
    } else {
      // IF ADD

      // not attribute template
      if (action?.templateId != "-15") {
        dispatch(setExpandedKeys([...expandedKeys, action.id]));
      }

      const selectedNode = searchRecursivelyByKey(treeData, action?.id, "key");

      if (selectedNode) {
        if (!selectedNode.isLeaf && !selectedNode.children) {
          await getAllNodes(action?.id).then((response: any) => {
            queryClient.setQueryData([GET_CHILDRENS, action.id], response);
            setTimeout(() => {
              addNewNodeRecursively(
                treeData,
                id,
                name,
                icon,
                action,
                getTemplateName(action?.templateId),
                action?.templateId == TEMP_GROUPING_NODE_ID
                  ? allowedChildren
                  : getAllowedChildrens(action?.templateId)
              );
            }, 600);
          });
        } else {
          addNewNodeRecursively(
            treeData,
            id,
            name,
            icon,
            action,
            getTemplateName(action?.templateId),
            action?.templateId == TEMP_GROUPING_NODE_ID
              ? allowedChildren
              : getAllowedChildrens(action?.templateId)
          );
        }
      } else {
        addNewNodeRecursively(
          treeData,
          id,
          name,
          icon,
          action,
          getTemplateName(action?.templateId),
          action?.templateId == TEMP_GROUPING_NODE_ID
            ? allowedChildren
            : getAllowedChildrens(action?.templateId)
        );
      }

      const previousData = queryClient.getQueryData([
        GET_CHILDRENS,
        parentMenuNodeId,
      ]);
      if (previousData) {
        updateQueryClientChildRecursively(previousData, id, name, action);
        queryClient.setQueryData(
          [GET_CHILDRENS, parentMenuNodeId],
          previousData
        );
      }
    }
  };

  const generateTotalDataRecursively = (
    data,
    allData: ITreeData[],
    nodeId,
    parentBreadcrumb: any[] = []
  ) => {
    const originalData = allData?.filter((node) => node.parentId == nodeId);
    const treeDataPromises = originalData?.map((node: ITreeData) => {
      const breadcrumbItem = {
        id: node.id,
        title: node.name,
        parentId: node.parentId || 0,
        templateName: getTemplateName(node.templateId),
        templateId: node.templateId,
      };

      const breadcrumb = [...parentBreadcrumb, breadcrumbItem];
      let childrenCache = [];

      childrenCache = allData?.filter((item) => item.parentId == node.id);

      return {
        key: node?.id,
        templateId: node.templateId,
        name: node.name,
        title: getTemplateName(node.templateId),
        icon: getTemplateIcon(node.templateId),
        breadcrumb: breadcrumb,
        parentId: node.parentId,
        isLeaf: node?.countChildren === 0,
        visualOrder: node?.visualOrder,
        flag: getFlags(node?.bitFlag),
        allowedChildren:
          node?.templateId == TEMP_GROUPING_NODE_ID
            ? node?.body?.find((body) => body?.type === "allowedChildren")
                ?.value
            : [],
        children:
          (childrenCache.length > 0 &&
            generateTotalDataRecursively(
              childrenCache,
              allData,
              node.id,
              breadcrumb
            )) ||
          [],
        countChildren: node?.countChildren,
        permissionsId: node?.permissionsId,
      };
    });

    // Wait for all promises to resolve
    return treeDataPromises;
  };

  const generateSearchDataRecursively = (
    data: ITreeData[],
    parentBreadcrumb: any[] = []
  ) => {
    const treeDataPromises = data?.map((node: ITreeData) => {
      const breadcrumbItem = {
        id: node.id,
        title: node.name,
        parentId: node.parentId || 0,
        templateName: getTemplateName(node.templateId),
        templateId: node.templateId,
      };

      const breadcrumb = [...parentBreadcrumb, breadcrumbItem];

      return {
        key: node?.id,
        templateId: node.templateId,
        name: node.name,
        title: getTemplateName(node.templateId),
        icon: getTemplateIcon(node.templateId),
        breadcrumb: breadcrumb,
        parentId: node.parentId,
        isLeaf: node?.countChildren === 0,
        visualOrder: node?.visualOrder,
        flag: getFlags(node?.bitFlag),
        allowedChildren:
          node?.templateId == TEMP_GROUPING_NODE_ID
            ? node?.body?.find((body) => body?.type === "allowedChildren")
                ?.value
            : [],
        children:
          (node?.children?.length > 0 &&
            generateSearchDataRecursively(node?.children, breadcrumb)) ||
          [],
        countChildren: node?.countChildren,
        permissionsId: node?.permissionsId,
      };
    });

    return treeDataPromises;
  };

  const generateDataRecursively = async (
    data: ITreeData[],
    parentBreadcrumb: any[] = []
  ) => {
    const treeDataPromises = data?.map(async (node: ITreeData) => {
      const breadcrumbItem = {
        id: node.id,
        title: node.name,
        parentId: node.parentId || 0,
        templateName: getTemplateName(node.templateId),
        templateId: node.templateId,
      };

      const breadcrumb = [...parentBreadcrumb, breadcrumbItem];
      let childrenCache = [];

      // if (node?.id > 0 || [-403, -100].includes(node?.id)) {
      if (expandedKeys?.includes(node?.id)) {
        childrenCache = await getAllNodes(node?.id?.toString());
      }

      return {
        key: node?.id,
        templateId: node.templateId,
        name: node.name,
        title: getTemplateName(node.templateId),
        icon: getTemplateIcon(node.templateId),
        breadcrumb: breadcrumb,
        permissionsId: node?.permissionsId,
        parentId: node.parentId,
        isLeaf: node?.countChildren === 0,
        visualOrder: node?.visualOrder,
        flag: getFlags(node?.bitFlag),
        allowedChildren:
          node?.templateId == TEMP_GROUPING_NODE_ID
            ? node?.body?.find((body) => body?.type === "allowedChildren")
                ?.value
            : [],
        children:
          (childrenCache.length > 0 &&
            (await generateDataRecursively(childrenCache, breadcrumb))) ||
          [],
        countChildren: node?.countChildren,
      };
    });

    // Wait for all promises to resolve
    return Promise.all(treeDataPromises);
  };
  // const parentPath = queryClient.getQueryData([
  //   GET_HIERARCHY_DETAILS,
  //   searchParams.get("nodeId"),
  // ]) as any;
  // const parentIds = getParentIds(parentPath?.path);

  // const indexOfCurrentNode = parentIds.findIndex(
  //   (item) => item === Number(parentMenuNodeId)
  // );

  // if (indexOfCurrentNode !== -1) {
  //   // to highlight selected hierarchy in trees on reload
  //   const hierarchy = metamodel
  //     ? parentIds
  //     : parentIds.slice(indexOfCurrentNode);

  //   if (hierarchy.length > 0) {
  //     expanded = [...hierarchy];
  //   }
  // }

  // allChildrens = (await getAllNodes(expanded?.join())) as INodes[];

  const handleRecursiveQueryClientDelete = (
    data,
    deleteKeys,
    parent = null
  ) => {
    const newData = data.filter((item) => !deleteKeys.includes(item.id));
    if (newData.length === 0 && parent) {
      parent.last = true;
    }

    newData.forEach((item) => {
      if (item.children && item.children.length > 0) {
        handleRecursiveQueryClientDelete(item.children, deleteKeys, item);
      }
    });

    return newData;
  };

  const updateNewIdRecursively = (data, oldRandomId, newId) => {
    const BreakException = {};
    try {
      data.forEach((item) => {
        if (item.key == oldRandomId) {
          const breadcrumbs = item?.breadcrumb?.map((crumb, index) => {
            // Create a shallow copy of each object in the array
            return index === item.breadcrumb.length - 1
              ? { ...crumb, id: newId } // Modify 'id' for the last object
              : { ...crumb }; // Keep other objects unchanged
          });
          item.breadcrumb = breadcrumbs;
          item.key = newId;

          // const displayedBreadcrumb = breadcrumb.map(({ ...data }) => data);
          // const index = displayedBreadcrumb.findIndex(
          //   (data) => data.id === item.key
          // );

          // if (index !== -1) {
          //   displayedBreadcrumb[index].id = newId;
          //   dispatch(setBreadcrumb(displayedBreadcrumb));
          // }
          throw BreakException;
        } else if (item.children && item.children.length > 0) {
          updateNewIdRecursively(item.children, oldRandomId, newId);
        }
      });
    } catch (e) {
      if (e !== BreakException) throw e;
    }
  };

  const deleteNodesRecursively = (
    data: ITreeData[],
    target: number,
    parent?: any
  ) => {
    const index = data?.findIndex((item) => item?.key === target);
    if (index !== -1) {
      data.splice(index, 1);
      if (data.length === 0 && parent) {
        parent.isLeaf = true;
      }
      if (parent) {
        parent.countChildren = parent.children?.length;
      }
      return data;
    }
    data.forEach((item) => {
      if (item.children && item.children.length > 0) {
        deleteNodesRecursively(item.children, target, item);
      }
    });
    return data;
  };

  const settingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  const pinnedMutation = useMutation(saveLocalSettings);

  const updatePinned = (newPinned) => {
    pinnedMutation.mutateAsync({
      value: {
        ...(settingsData?.body ? settingsData?.body[0]?.value || {} : {}),
        pinned: newPinned,
      },
    });
    dispatch(setPinned([...newPinned]));
  };

  const deleteNode = (treeData, selectedKeys) => {
    const newPinned = [];
    pinned?.forEach((pin) => {
      if (selectedKeys.includes(pin.id)) {
        newPinned.push({ ...pin, inTrash: true });
      } else {
        newPinned.push({ ...pin });
      }
    });
    selectedKeys?.forEach((toDeleteId) => {
      // if (
      //   pinned?.findIndex((pinnedItem) => pinnedItem?.id === toDeleteId) !== -1
      // ) {
      //   deletedPinned.push();
      // }

      // mutation.mutateAsync({
      //   value: {
      //     ...(settingsData?.body ? settingsData?.body[0]?.value || {} : {}),
      //     pinned: newFavorites,
      //   },
      // });
      // dispatch(setPinned([...newFavorites]));
      // notification.success({
      //   message: t("Removed from favorites!"),
      // });
      deleteNodesRecursively(treeData, toDeleteId);
    });

    if (newPinned.length > 0) {
      updatePinned(newPinned);
    }
  };

  const pathname =
    import.meta.env.VITE_APP_BASE_URL !== "/"
      ? location.pathname.replace(import.meta.env.VITE_APP_BASE_URL, "")
      : location.pathname;

  const updateDisabledTemplates = async (data: ITreeData[]) => {
    (await getAllNodes(DISABLED_METAMODEL_NODE_ID.toString()).then(
      (childrens: any) => {
        queryClient.setQueryData(
          [GET_CHILDRENS, DISABLED_METAMODEL_NODE_ID.toString()],
          childrens
        );
        const findAndUpdate = (items) => {
          for (const item of items) {
            if (item.key == DISABLED_METAMODEL_NODE_ID) {
              item.isLeaf = childrens?.length === 0;
              const newBreadcrumbs = item?.breadcrumb?.map(
                (breadcrumbItem, index) =>
                  index === item.breadcrumb.length - 1
                    ? { ...breadcrumbItem, title: item?.name }
                    : breadcrumbItem
              );

              item.children =
                childrens?.map((node: ITreeData) => {
                  const breadcrumbItem = {
                    id: node.id,
                    title: node.name,
                    parentId: node.parentId || 0,
                    templateName: getTemplateName(node.templateId),
                    templateId: node.templateId,
                    allowedChildrens: getAllowedChildrens(node.templateId),
                  };

                  const breadcrumb = [...newBreadcrumbs, breadcrumbItem];

                  return {
                    key: node?.id,
                    templateId: node.templateId,
                    name: node.name,
                    title: node.name,
                    icon: getTemplateIcon(node.templateId),
                    breadcrumb: breadcrumb,
                    parentId: node.parentId,
                    isLeaf: node?.countChildren === 0,
                    flag: getFlags(node?.bitFlag),
                    children: [],
                  };
                }) || [];
              return true;
            }

            if (item.children && item.children.length > 0) {
              if (findAndUpdate(item.children)) {
                return true; // Stop recursion if item was found and renamed
              }
            }
          }
          return false; // Item not found in this branch
        };

        findAndUpdate(data);
        return data;
      }
    )) as any[];
  };

  const restoreTemplates = async (data: ITreeData[], restoredIds) => {
    queryClient.invalidateQueries([GET_CHILDRENS, "-403"]);

    (await getAllNodes(restoredIds[1]).then((childrens: any) => {
      queryClient.setQueryData(
        [GET_CHILDRENS, restoredIds[1].toString()],
        childrens
      );

      const findAndUpdate = (items) => {
        for (const item of items) {
          if (item.key == restoredIds[1]) {
            item.isLeaf = false;
            const newBreadcrumbs = item?.breadcrumb?.map(
              (breadcrumbItem, index) =>
                index === item.breadcrumb.length - 1
                  ? { ...breadcrumbItem, title: item?.name }
                  : breadcrumbItem
            );

            const newChildrens =
              childrens?.map((node: ITreeData) => {
                const breadcrumbItem = {
                  id: node.id,
                  title: node.name,
                  parentId: node.parentId || 0,
                  templateName: getTemplateName(node.templateId),
                  templateId: node.templateId,
                  allowedChildrens: getAllowedChildrens(node.templateId),
                };
                const alreadyPresentNode = item?.children?.find(
                  (_node) => _node.key == node.id
                );

                if (alreadyPresentNode) {
                  return { ...alreadyPresentNode };
                }

                const breadcrumb = [...newBreadcrumbs, breadcrumbItem];

                return {
                  key: node?.id,
                  templateId: node.templateId,
                  name: node.name,
                  title: node.name,
                  icon: getTemplateIcon(node.templateId),
                  breadcrumb: breadcrumb,
                  parentId: node.parentId,
                  isLeaf: node?.countChildren === 0,
                  flag: getFlags(node?.bitFlag),
                  children: [],
                };
              }) || [];

            item.children = [...newChildrens];

            return true;
          }

          if (item.children && item.children.length > 0) {
            if (findAndUpdate(item.children)) {
              return true; // Stop recursion if item was found and renamed
            }
          }
        }
        return false; // Item not found in this branch
      };

      if (restoredIds[1] == -1) {
        const newChildrens =
          childrens?.map((node: ITreeData) => {
            const breadcrumbItem = {
              id: node.id,
              title: node.name,
              parentId: node.parentId || 0,
              templateName: getTemplateName(node.templateId),
              templateId: node.templateId,
              allowedChildrens: getAllowedChildrens(node.templateId),
            };
            const alreadyPresentNode = data?.find(
              (_node) => _node.key == node.id
            );

            if (alreadyPresentNode) {
              return { ...alreadyPresentNode };
            }

            const breadcrumb = [breadcrumbItem];

            return {
              key: node?.id,
              templateId: node.templateId,
              name: node.name,
              title: node.name,
              icon: getTemplateIcon(node.templateId),
              breadcrumb: breadcrumb,
              parentId: node.parentId,
              isLeaf: node?.countChildren === 0,
              flag: getFlags(node?.bitFlag),
              children: [],
            };
          }) || [];

        return newChildrens;
      }
      findAndUpdate(data);
      return data;
    })) as any[];
  };

  // function to delete multiple nodes
  const handleNodeDelete = (treeData, highlightDisable) => {
    deleteNode(treeData, selected.keys);

    if (treeData.length > 0) {
      if (highlightDisable) {
        navigate(`${pathname}?nodeId=${selected.keys[0]}`);
        dispatch(
          setSelected({
            keys: [selected.keys[0]],
            info: [{ ...selected.info[0] }],
          })
        );
        dispatch(setRefreshBreadcrumbs(true));
      } else {
        navigate(`${pathname}?nodeId=${treeData[0].key}`);
        dispatch(
          setSelected({
            keys: [treeData[0].key],
            info: [
              {
                id: treeData[0].key,
                parentId: treeData[0].parentId,
                name: treeData[0].name,
                isLeaf: treeData[0].isLeaf,
              },
            ],
          })
        );
        dispatch(setBreadcrumb(treeData[0].breadcrumb));
      }
    } else {
      navigate(`${pathname}`);
      dispatch(setBreadcrumb([]));
      dispatch(
        setSelected({
          keys: [],
          info: [],
        })
      );
    }

    let allExpanded = [...expandedKeys];
    selected.info?.forEach((item) => {
      allExpanded = allExpanded.filter((expanded) => expanded !== item.id);
    });
    if (metamodel) {
      dispatch(
        setExpandedKeys([...allExpanded, DISABLED_METAMODEL_NODE_ID, -100])
      );
    } else {
      dispatch(setExpandedKeys([...allExpanded]));
    }

    const previousData = queryClient.getQueryData([
      GET_CHILDRENS,
      parentMenuNodeId.toString(),
    ]) as any[];

    const updatedQueryCacheData = handleRecursiveQueryClientDelete(
      [...previousData],
      selected.keys
    );

    queryClient.setQueryData(
      [GET_CHILDRENS, parentMenuNodeId.toString()],
      updatedQueryCacheData
    );
  };

  return {
    updateChildNode,
    generateDataRecursively,
    handleNodeDelete,
    updateNewIdRecursively,
    generateSearchDataRecursively,
    updateNodeFromTrash,
    updateNodeAfterPublish,
    updateDisabledTemplates,
    restoreTemplates,
    generateTotalDataRecursively,
  };
};

export { useSidebarActions };
