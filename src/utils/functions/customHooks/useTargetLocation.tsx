import { useState, useEffect } from "react";
import { useQuery } from "react-query";
import { getParentNameDetails } from "../../../services/node";

/**
 * Target Location Attribute IDs
 *
 * These specific attribute IDs (-322, -389, -417, -337) are fixed in the backend system
 * and represent target location attributes used for connectors. When these attributes
 * have a valid positive numeric ID as their value, we need to fetch and display the
 * corresponding node name in a second column.
 * @link https://valuetank.atlassian.net/wiki/spaces/C3K/pages/234979329/Frontend+hard-coded+rules
 */
export const TARGET_LOCATION_ATTRIBUTE_IDS = [-322, -389, -417, -337];

/**
 * Extracts a valid numeric target location ID from various value types.
 */
function extractTargetLocationId(val: any): number | null {
  if (typeof val === "number") return val;
  if (typeof val === "string" && val.trim() !== "" && !isNaN(Number(val)))
    return Number(val);
  if (
    val &&
    typeof val === "object" &&
    "id" in val &&
    typeof val.id === "number"
  )
    return val.id;
  return null;
}

/**
 * Custom hook to fetch target location details with debounce and isolation.
 * Accepts any value and extracts the ID internally.
 *
 * @param value The value of the attribute (number, string, or object)
 * @param debounceMs Debounce delay in ms (default 400)
 * @returns The name of the target location or null if not found, loading state, and spinner flag
 */
export const useTargetLocation = (value: any, debounceMs = 400) => {
  const [debouncedValue, setDebouncedValue] = useState<any>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, debounceMs);
    return () => clearTimeout(handler);
  }, [value, debounceMs]);

  const id = extractTargetLocationId(debouncedValue);

  const { data, isLoading, isError } = useQuery(
    ["targetLocationName", id],
    async () => {
      if (!id || typeof id !== "number" || id <= 0) return null;
      const response = await getParentNameDetails(id);
      return response && response.length > 0 ? response[0].name : null;
    },
    {
      staleTime: Infinity,
      cacheTime: Infinity,
    }
  );

  // Show spinner only if id is set and loading
  const showSpinner = !!id && isLoading;

  return { targetLocationName: data, isLoading, isError, showSpinner };
};
