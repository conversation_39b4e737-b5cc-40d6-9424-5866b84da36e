import { i18n } from "../../utils/i18n";

export const transformObjectPath = (path: string, inTrash: boolean) => {
  if (!path) {
    return "";
  }

  // Original cleanup logic - preserve this exactly
  const transformedText = path.replace(/{-?\d+}|[#|]/g, (match) =>
    match.startsWith("{") ? " > " : ""
  );

  // Split by > separator (handle any spacing inconsistencies)
  const parts = transformedText
    .split(/\s*>\s*/) // Split by > with optional spaces around it
    .map((part) => part.trim()) // Trim each part
    .filter((part) => part.length > 0); // Remove empty parts

  // Remove the last element if we have multiple parts
  if (parts.length > 1) {
    parts.pop();
  } else {
    // If only one part, return empty string
    return inTrash ? `${i18n.t("Trash")}` : "";
  }

  // Join back with consistent spacing
  const result = parts.join(" > ");

  return inTrash ? `${i18n.t("Trash")} > ${result}` : result;
};
