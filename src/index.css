p,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
}

html,
body {
  height: 100%;
  position: relative;

  font-size: 13px;

  font-weight: 400;
  overflow: hidden;
}

* {
  font-family: "Poppins", sans-serif;
}

#root {
  height: 100%;
  display: flex;
  flex-direction: column;
}

body {
  margin: 0px;
}

.node-motion {
  transition: all 0.3s;
  overflow-y: hidden;
}

.ant-dropdown-menu-submenu-title .anticon {
  color: var(--color-text);
}

.ant-dropdown-menu-item .anticon,
.ant-dropdown-menu-submenu-title .anticon {
  font-size: 16px !important;
}

.quill {
  max-width: min-content;
}

.edit-attribute-modal {
  width: fit-content !important;
}

.edit-attribute-modal .ant-modal-body {
  padding-bottom: 20px;
  padding-top: 10px;
}

.ql-editor {
  min-height: 120px;
  overflow: auto;
  width: 700px;
  min-width: auto;
  resize: both;
}

.ql-toolbar {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  background: #ebf1f8;
  padding: 4px;
}

.ql-container {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

.ql-editor p {
  color: #515151;
}

.ant-modal-content {
  padding: 0px !important;
}

.ant-modal-header {
  min-height: 38px;
  display: flex;
  align-items: center;
  padding: 0px 20px !important;
  background: #ccdaec !important;
}

.ant-modal-title {
  font-size: 13px !important;
  font-weight: 500 !important;
  color: #094f8a !important;
}

.ant-modal-body {
  padding: 0px 20px !important;
}

.ant-modal-close-icon {
  color: #094f8a;
}

.ant-modal-footer {
  padding-right: 20px !important;
  padding-left: 20px !important;
  padding-bottom: 20px !important;
}

.ant-modal-close {
  top: 9px !important;
}

.bottom-navbar .indicator {
  top: -14px;
}

.hide-indicator .indicator {
  display: none;
}

.breadcrumb-button {
  font-size: 13px;
  gap: 6px;
  height: 24px;
  padding: 0px 15px;
  border-radius: 3px;
}

.ant-dropdown-menu-item {
  min-width: 160px;
  color: var(--color-text) !important;
}

.ant-dropdown-menu-title-content {
  font-size: 13px;
  color: var(--color-text);
}

.ant-select-dropdown {
  min-width: 100px !important;
  z-index: 20050 !important;
}

.ant-tooltip-inner {
  font-size: 12px;
}

.ant-tooltip {
  z-index: 8000;
}

.graph-tooltip {
  position: absolute;
  background-color: white;
  border: 1px solid var(--color-text);
  color: var(--color-text);
  padding: 5px;
  border-radius: 5px;
  font-size: 12px;
  z-index: 10;
  pointer-events: none;
}

.ant-float-btn {
  inset-block-end: 30px;
}

.hide-draggable-icon .ant-tree-draggable-icon {
  display: none;
}

.ant-breadcrumb {
  font-size: 13px;
}

.ant-breadcrumb-link .ant-dropdown-trigger {
  color: #fff;
  font-weight: 400;
}

.ant-modal-wrap {
  z-index: 10000;
}

.ant-breadcrumb-link .ant-dropdown-trigger:hover {
  color: #fff;
}

.version-info {
  font-size: 12px;
  text-align: right;
  margin-top: 5px;
  color: grey;
}

.add-item {
  border: 1px dashed #b3b5b5;
  color: #929292;
  padding: 5px 16px 5px 16px;
  border-radius: 2px;
  cursor: pointer;
  margin-top: 10px;
  display: block;
}

.ant-breadcrumb .active {
  color: #fff100 !important;
  font-weight: 600;
  letter-spacing: 0.2px;
}

.ant-breadcrumb .active:hover {
  color: #fff100;
}

.ant-tree-treenode-selected::before {
  background: none !important;
}

.ant-tree-treenode {
  width: 100%;
}

.ant-tree-node-content-wrapper {
  flex: 1;
  display: flex;
}

.ant-tree-node-content-wrapper::before {
  background-color: transparent !important;
}

.ant-tree-node-content-wrapper:hover {
  background: #eee !important;
}

.ant-tree-treenode::before {
  background: none !important;
}

.ant-tree {
  font-size: 13px;
}

.ant-result-404 {
  flex: 1;
}

.tree-collapse .ant-collapse-header {
  padding: 0px !important;
  border-radius: 0px !important;
}

.tree-collapse,
.tree-collapse .ant-collapse-item {
  border-radius: 0px !important;
}

.tree-collapse .ant-collapse-item {
  border-bottom: none !important;
}

.tree-collapse {
  border: none;
  background-color: #fff;
}

.tree-collapse .ant-collapse-expand-icon svg {
  transition: all 0.4s ease;
}

.tree-collapse .ant-collapse-expand-icon {
  position: absolute;
  left: 6px;
  z-index: 1;
  top: 10%;
}

.tree-collapse .ant-collapse-expand-icon svg {
  width: 12px;
  height: 12px;
}

.tree-collapse .ant-collapse-expand-icon path {
  stroke: #4277a2;
}

.tree-collapse .ant-collapse-header-text>div>h6 {
  padding-left: 24px;
}

.tree-collapse .ant-collapse-content {
  border-top: none !important;
}

.tree-collapse .ant-collapse-content-box {
  padding: 0px !important;
}

.tree-collapse .ant-collapse-content-box>div>h6 {
  padding-left: 23px;
}

.ant-popover {
  z-index: 1000000 !important;
}

.ant-popover * {
  font-size: 13px;
}

.ant-image-mask-info {
  font-size: 12px;
}

.attributes-wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.ant-modal-footer>.ant-btn-default {
  display: none;
}

.with-cancel-button .ant-modal-footer>.ant-btn-default {
  display: inline-block;
}

.draggable-modal .p-dialog-header {
  background-color: var(--color-light);
  padding: 7px 20px;
}

.draggable-modal {
  width: 50%;
}

.grouping-allowed-children {
  z-index: 10000 !important;
}

.draggable-modal .p-dialog-content {
  padding-top: 8px;
  padding-bottom: 0px;
}

.draggable-modal .p-dialog-title {
  font-size: 13px !important;
  font-weight: 500 !important;

  color: var(--color-text);
}

.export-modal .ant-modal-footer {
  margin-top: 0px;
  padding-top: 15px;
}

.details-modal .ant-modal-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.details-modal .ant-modal-body {
  height: 100%;
  padding: 0px !important;
  overflow: hidden !important;
}

.ant-tree-switcher svg {
  transition: all 0.3s ease;
}

.attribute-popup .ant-popover-inner {
  max-height: 450px;
  overflow-y: auto;
}

.add-attribute-popup .ant-popover-inner-content {
  min-width: 430px;
}

.drawer-sidebar .ant-drawer-content-wrapper {
  width: 248px !important;
}

.drawer-sidebar .ant-drawer-header {
  display: none;
}

.drawer-sidebar .ant-drawer-body {
  padding: 0px;
  margin-top: 40px;
}

.drawer-sidebar ul {
  max-width: unset;
}

.drawer-sidebar .anticon-menu-fold {
  left: unset;
  right: 9px;
}

.p-dialog-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text);
}

.ag-watermark {
  display: none;
}

.ant-image-preview-mask {
  z-index: 5001 !important;
}

.ant-image-preview-wrap,
.ant-image-preview-operations-wrapper {
  z-index: 5002 !important;
}

.filter-popup {
  width: 60%;
}

.ant-table th {
  font-size: 11px;
  font-weight: 600 !important;
  text-transform: uppercase;
  padding: 0px !important;
}

/* .ant-table tr:hover td {
  background-color: #f7f7f7 !important;
} */
.ant-table td {
  font-size: 12px;
  font-weight: 400 !important;
  padding: 6px !important;
}

.filter-popup .ant-popover-inner {
  background-color: #fff;
}

.filter-popup .ant-popover-arrow::before {
  background-color: #fff;
}

.ant-notification-notice-message {
  margin-bottom: 0px !important;
  font-size: 14px !important;
}

.ant-notification-notice {
  padding: 16px 24px !important;
}

.ant-notification {
  top: 90px !important;
}

.ant-notification-notice-description {
  margin-top: 4px !important;
  font-size: 13px !important;
}

.version-tab>.ant-tabs-nav {
  margin-bottom: 0px;
}

.expanded-sidebar {
  width: 100% !important;
  min-width: 100% !important;
  position: relative;
  max-width: 100% !important;
}

.version-tab {
  overflow-y: hidden;
  min-height: 30px;
}

.version-tab .ant-tabs-tab {
  font-size: 12px;
  padding: 4px 14px !important;
  background: #fff !important;
  border-color: #fff !important;
}

.version-tab .ant-tabs-tab span {
  color: #858080;
}

.version-tab .ant-tabs-nav-wrap {
  margin-top: 3px;
  margin-left: 1px;
}

.ag-filter-apply-panel {
  gap: 10px;
}

.ag-filter-apply-panel button {
  margin-left: 0px;
  font-size: 12px;
  flex: 1;
}

.top-header-menu {
  min-width: 200px;
  z-index: 5001;
}

.top-header-menu li {
  font-size: 13px;
  height: 32px !important;
  line-height: 32px !important;
  color: var(--color-text);
  padding: 0px !important;
}

.top-header-menu>li {
  margin-right: 20px !important;
}

.ant-modal-confirm-body-wrapper {
  padding: 20px 0px;
}

.top-header-menu>li a {
  padding: 0px !important;
}

.top-header-menu a {
  display: block;
  width: 100%;
  padding: 0px 16px;
  color: var(--color-text);
}

.header-menus {
  width: 100%;
  display: flex;
  gap: 10px;
  align-items: center;
  color: var(--color-text);
}

.header-menus .new-tab {
  transform: scale(0.8);
  margin-right: -6px;
  fill: var(--color-text);
}

.top-header-menu .ant-menu-item a {
  color: var(--color-text) !important;
}

.top-header-menu li a::before {
  position: relative !important;
}

.ant-tree-list .dragging::after {
  border: none !important;
  box-shadow: none !important;
}

.deleted-hyperlink {
  text-decoration: line-through;
  color: rgb(135, 135, 135);
}

.template-container {
  display: flex;
  align-items: center;
  gap: 6px;
  text-align: left;
}

.template-container img {
  width: 18px;
  height: 18px;
  object-fit: contain;
}

.comment-rating {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  margin: 0 auto;
  text-align: center;
}

.negative-rating {
  background-color: #ff8372;
}

.positive-rating {
  background-color: #00978a;
}

.tox-menu {
  /* transform: translate(-50%, 0px) !important; */
  /* left: 50% !important; */
}

.tox-tinymce-aux,
.tox-fullscreen {
  z-index: 8000 !important;
}

.ant-menu-submenu-popup .favorite-items {
  display: flex !important;
  align-items: center !important;
  margin-right: 10px !important;
}

.ant-menu-submenu-popup .favorite-items img {
  object-fit: contain;
}

.ant-menu-submenu-popup .submenu-title {
  padding: 0px 16px;
}

.ant-menu-submenu .ant-menu-title-content {
  color: var(--color-text);
  font-size: 13px;
}

.ant-menu-submenu-title .submenu-title {
  padding: 0px !important;
}

.top-header-menu .ant-menu-title-content {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.top-header-menu .ant-menu-submenu-title {
  height: 32px !important;
  line-height: 32px !important;
}

.top-header-menu ul {
  padding-bottom: 8px !important;
  padding-top: 4px !important;
}

.ant-tree-node-selected {
  background-color: var(--color-light) !important;
}

.ant-table-row-selected>td {
  background: #f5f6f7 !important;
}

.top-header-menu .ant-menu-item-selected {
  color: #094f8a !important;
  background-color: #dbdbdb !important;
}

.top-header-menu .folder-icon {
  display: none;
}

.bottom-drawer-details {
  position: relative !important;
}

::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #aaa;
  border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

thead tr {
  background-color: #edf2f8;
}

thead .ant-table-cell-scrollbar {
  box-shadow: none !important;
  padding: 0px !important;
  display: block !important;
  border-bottom: 0px !important;
}

.ant-dropdown-menu-item-disabled>span {
  color: #a6a5a5;
}

.react-resizable {
  position: relative;
  background-clip: padding-box;
}

.react-resizable-handle {
  position: absolute;
  width: 10px;
  height: 100%;
  bottom: 0;
  right: -5px;
  cursor: col-resize;
  z-index: 1;
}

.dragHandler:hover {
  cursor: move;
}

.hide-scroll {
  overflow-x: hidden;
}

.ant-table-wrapper .ant-table-thead th.ant-table-column-sort::before {
  background-color: #cac5c5 !important;
}

.ant-table th::before {
  background-color: #cac5c5 !important;
}

:where(.css-dev-only-do-not-override-pkof6o).ant-table-wrapper .ant-table-thead th.ant-table-column-has-sorters:hover::before {
  background-color: #cac5c5 !important;
}

.version-tab .ant-tabs-nav-list {
  transform: none !important;
}

.version-tab .ant-tabs-nav-operations {
  display: none !important;
}

.ant-dropdown-menu {
  padding: 7px !important;
}

.ant-dropdown-menu-item {
  padding: 6px 12px !important;
}

.ant-select-item-option-selected {
  background-color: #a3b0b52b !important;
  /* margin-bottom: 3px; */
}

.multi-select .ant-select-item-option-selected {
  flex-direction: row-reverse !important;
  gap: 10px;
  /* background-color: red !important; */
}

.ant-select-item-option-state {
  color: green !important;
}

.readonly-checkbox .p-checkbox-box {
  background: white;
  border-width: 1px;
  width: 18px;
  height: 18px;
  cursor: default;
}

.readonly-checkbox .p-checkbox-input {
  cursor: default;
}

.readonly-checkbox .p-checkbox-icon {
  color: #3b82f6;
  cursor: default;
}

.react-draggable .ant-modal-title {
  width: 100%;
  height: 100%;
}

.react-draggable .ant-modal-body {
  overflow: auto;
}

.react-draggable .ant-modal-content {
  overflow: auto;
  resize: both;
  width: 1000px;
  /* height: 70vh; */
  min-height: 100px;
  min-width: 400px;
  max-width: 100%;
  max-height: 96vh;
}

.details-modal {
  width: unset !important;
}

.right-align {
  margin-left: auto;
  width: fit-content;
}

.ant-cascader-menu-item-active,
.ant-picker-time-panel-cell-selected>div {
  background-color: #dfe9f1 !important;
}

.ant-modal-mask {
  z-index: 10000 !important;
}

.ant-modal-wrap {
  z-index: 10000 !important;
}

.mask {
  position: absolute;
  background: #505a6240;
  overflow: hidden;
  z-index: 6005;
}

.required-bg {
  background-color: #ff00001a !important;
}

.required-bg>h6 {
  background-color: #ff00001a !important;
}

.ag-full-width-container>.ag-row-position-absolute>div {
  background: white !important;
  padding: 6px !important;
}

.ag-row-group-expanded {
  border-bottom: none;
}

.trash-mask {
  position: fixed;
  background: #505a6240;
  overflow: hidden;
  z-index: 3000;
}

.trash-bottom-drawer-mask .ant-tabs-content-holder {
  background-color: #fff;
  z-index: 3001;
}

.ant-menu-inline-collapsed .remove-from-pinned {
  display: none;
}

.cxtmenu {
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
}

/* Customize the context menu item appearance */
.cxtmenu-item {
  font-size: 14px !important;
  padding: 8px 12px !important;
  min-width: 150px;
}

/* Customize the context menu item on hover */
/* .cxtmenu-item:hover {
  background-color: #e1e1e1 !important;
  color: #4377a2 !important;
} */

/* Customize the context menu separator line */
.cxtmenu-divider {
  border-top: 1px solid #ccc !important;
}

.__________cytoscape_container {
  height: 100% !important;
}

#cy>div:first-child {
  width: 100% !important;
}

.rc-virtual-list-scrollbar-show {
  display: block !important;
}

.hidden {
  display: none !important;
}

.ql-tooltip {
  left: 5px !important;
}

.p-column-filter-buttonbar button {
  padding: 6px 10px;
}

.developer-mode-on {
  width: fit-content;
  position: fixed;
  z-index: 1000000;
  /* top: 7px; */
  /* left: 0px; */
  right: 10px;
  margin: auto;
  bottom: 20px;
}

.dev-mode-alert {
  width: fit-content;
  position: fixed;
  z-index: 100000;
  font-size: 12px;
  padding: 1px 6px;
  right: 4px;
  margin: auto;
  top: 2px;
}

#cy {
  position: relative;
  margin: auto;
  background: #fff;
}

.p-tabview-panels {
  padding: 10px;
}

.p-tabview-nav .p-tabview-nav-link {
  color: #084375;
  padding: 12px 10px;
  font-weight: 500;
}

.p-tabview-ink-bar {
  background-color: #084375;
}

.p-overlaypanel-content {
  min-width: 400px;
  max-width: 400px;
}

.w-full {
  width: 100%;
}

.ant-notification {
  z-index: 1000000000;
}

.p-dialog-mask {
  z-index: 7001 !important;
}

.breadcrumb-button {
  background-color: var(--color-text) !important;
  border: none;
}

.breadcrumb-button:hover {
  opacity: 0.8;
}

.p-button.primary-button {
  background: var(--color-text);
  border: 1px solid;
  color: #fff;
}

.p-button.primary-button .anticon {
  margin-right: 0.5rem;
}

.p-button.primary-button span {
  font-weight: 500;
}

#multiselect-wrapper .p-multiselect-panel {
  position: relative !important;
  left: unset !important;
  top: unset !important;
}

#select-wrapper .ant-select-dropdown {
  position: relative !important;
  inset: unset !important;
  width: 100% !important;
  margin-top: 10px;
}

.mockup {
  font-size: 12px !important;
}

.trash-hyperlink {
  color: var(--color-trash) !important;
  /* text-decoration: line-through !important; */
}

.top-header-menu li::after {
  width: 85%;
  margin: auto;
  inset-inline: 0px !important;
}

.initial-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 40px;
}

.ant-tour-mask {
  z-index: 5001 !important;
}

.ant-tour {
  z-index: 5002 !important;
}

.ant-tour-placeholder-animated {
  pointer-events: none;
}

.ant-tour-target-placeholder {
  pointer-events: all !important;
  z-index: 100000 !important;
}

.ant-tree-switcher:hover::before,
.ant-tree-switcher:hover {
  background-color: transparent !important;
}

.cancel-button {
  background-color: #9d2323 !important;
}

.cancel-button:hover {
  background-color: #de3838 !important;
}

.save-button {
  background-color: #3a835e !important;
}

.save-button:hover {
  background-color: #3f8b65 !important;
}

.padding-0 {
  padding: 0px !important;
}

.tree-count {
  position: absolute;
  right: 0px;
  top: 0px;
  bottom: 0px;
  margin: auto;
  min-width: 23px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  max-height: 15px;
  background-color: var(--color-light);
  color: var(--color-text);
}

.tree-container {
  height: 100%;
  min-height: 200px;
  overflow: auto;
  position: relative;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.allowed-children-select .p-multiselect-close {
  display: none;
}

.ant-tree-node-content-wrapper {
  border-radius: 8px !important;
}

.ag-cell:focus {
  border: none !important;
}

.ag-cell-focus {
  border: none !important;
}

.ant-picker-dropdown {
  z-index: 10000;
}

.ag-grid-edit-actions {
  width: 50px;
  flex: unset;
  display: flex;
  gap: 3px;
  justify-content: center;
}

.ag-grid-edit-actions .anticon-edit {
  font-size: 16px;
  color: var(--color-text);
}

.ag-grid-edit-actions button {
  font-size: 12px;
  width: 100%;
  height: 28px;
  box-shadow: none;
  background-color: white;
}

.ag-grid-edit-actions button:disabled {
  color: white;
  opacity: 0.8;
}

.save-action {
  color: green;
}

.save-action:hover {
  color: green !important;
  opacity: 0.8;
  background-color: white !important;
}

.cancel-action {
  color: #d3a706;
}

.cancel-action:hover {
  background-color: white !important;
  color: #d3a706 !important;
  opacity: 0.8;
}

.error-empty svg>g>g {
  stroke: #ffc9c9;
}

.error-empty .ant-empty-description {
  color: #fda5a5;
}

.new-attributes {
  border: 0.5px dashed #09437561;
  display: flex;
  width: 100%;
  border-radius: 4px;
}

.new-attributes p {
  font-size: 13px;
  cursor: pointer;
  font-weight: 400;
  padding: 8px 10px;
  border-right: 1px solid #eaeaea;
  max-width: 80%;
  background-color: var(--color-light);
  color: var(--color-text);
}

.sql-editor {
  border: 1px solid #d9d9d9;
  padding: 4px 11px !important;
  box-shadow: none;
  border-radius: 6px;
}

.sql-editor textarea {
  outline: none;
  padding: 4px 12px !important;
}

/* Make the Ant Design Tree scrollbar always visible.
 * This is a workaround solution because when `virtual` is enabled,
 * the scrollbar visible for small amont of time and it disappears.
 * The requirement is to make it always visible.
 */
.ant-tree-list-scrollbar {
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.ant-tree-list-scrollbar:not(.ant-tree-list-scrollbar-visible) {
  width: 8px !important;
  right: 0px !important;
}

.ant-tree-list-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.4) !important;
  border-radius: 4px !important;
}

.ant-select-selection-item[title] {
  pointer-events: none;
}