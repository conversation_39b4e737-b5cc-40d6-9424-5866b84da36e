import { createSlice } from "@reduxjs/toolkit";
import { IBreadcrumbs } from "../../interfaces";

export interface BreadcrumbState {
  parentBreadcrumbs: IBreadcrumbs[];
  tempBreadcrumbs: IBreadcrumbs;
  refreshBreadcrumbs: boolean;
  hideAllpathNames: boolean;
}

const initialState: BreadcrumbState = {
  parentBreadcrumbs: [],
  tempBreadcrumbs: null,
  refreshBreadcrumbs: false,
  hideAllpathNames: false,
};

export const breadcrumbSlice = createSlice({
  name: "breadcrumb",
  initialState,
  reducers: {
    setRefreshBreadcrumbs: (state, action) => {
      state.refreshBreadcrumbs = action.payload;
    },
    setTempBreadcrumbs: (state, action) => {
      state.tempBreadcrumbs = action.payload;
    },

    setParentBreadcrumbs: (state, action) => {
      state.parentBreadcrumbs = action.payload;
    },
    setHideAllPathNames: (state, action) => {
      state.hideAllpathNames = action.payload;
    },
    // setBreadcrumb: (state, action) => {
    //   state.breadcrumb = action.payload;
    // },
  },
});

export const {
  setParentBreadcrumbs,
  setTempBreadcrumbs,
  setRefreshBreadcrumbs,
  setHideAllPathNames,
} = breadcrumbSlice.actions;

export default breadcrumbSlice.reducer;
