import { COMMENT_TEMPLATE_ID } from "../constants";
import {
  IBitFlags,
  IHierarchyData,
  INodeDetails,
  ITreeData,
} from "../interfaces";
import { API } from "../utils/api";

export const getPermissionUsers = async (
  templateId: number[] | number
): Promise<INodeDetails[]> => {
  if (
    typeof templateId === "number" ||
    (Array.isArray(templateId) && templateId.length > 0)
  ) {
    const response = (await API.post(`/model/node/get`, {
      templateIds: typeof templateId === "number" ? [templateId] : templateId,
      addBody: true,
    })) as INodeDetails[];
    return response;
  }
};

export const deletePermissionUser = async (personIds: number[]) => {
  return API.post("/permissions/user/delete", personIds);
};

export const convertToAuthor = async (personIds: number[]) => {
  return API.patch("/permissions/user/convert2author", personIds);
};

export const convertToReader = async (personIds: number[]) => {
  // This uses the same endpoint as delete, as per requirements
  return API.post("/permissions/user/delete", personIds);
};

export const addNodeService = (values) => {
  const characteristics = {
    name: values.name,
    nodeType: values?.nodeType || "DATA",
    templateId: Number(values.templateId) || 0,
    body: values?.body || [],
    // allowedChildren: values?.allowedChildren || [],
  };

  return API.post("/model/node/new", {
    node: characteristics,
    parentId: Number(values.id),
    type: values.type,
  });
};

export const addPermissionUser = (payload: unknown) => {
  return API.post<unknown>("/permissions/user/new", payload);
};

export const addAttributes = (values) => {
  return API.patch(`/model/node/update`, {
    id: values.id,
    body: values.body,
  });
};

export const editNodeService = (values) => {
  return API.patch(`/model/node/rename`, {
    id: values.id,
    oldName: values.oldName,
    newName: values.newName,
  });

  // return API.patch(`/nodes/${values.id}/rename`, {
  //   oldName: values.oldName,
  //   newName: values.newName,
  // });
};

export const deleteNodeService = (values) => {
  return API.delete(`/model/node/delete`, {
    data: {
      ids: [values.id],
      confirm: true,
    },
  });
};

// export const deleteMetamodelService = (values) => {
//   return API.get(`/metamodel/${values.id}/delete`);
// };

export const restoreNodes = (values) => {
  return API.post(`/model/node/trash/restore2original`, values) as any;
};

export const restoreTemplates = (values) => {
  return API.post(`/metamodel/template/${values}/activate`) as any;
};

export const deleteMultipleNodeService = (values) => {
  return API.delete(`/model/node/delete`, {
    data: {
      ids: values.keys,
      confirm: true,
    },
  });
};

export const getAllCommentsDetails = async (
  ids: string[]
): Promise<INodeDetails[]> => {
  if (ids.length > 0) {
    const response = (await API.post(`/model/node/get`, {
      ids: ids,
      addBody: true,
    })) as INodeDetails[];
    return response;
  }
};

export const getAllNodesWithBody = async (id: string): Promise<ITreeData[]> => {
  if (id) {
    const response = (await API.post(`/model/node/get`, {
      parentIds: id.toString()?.includes(",") ? id.split(",") : [id],
      addBody: true,
    })) as ITreeData[];
    return response?.sort((a, b) => a.visualOrder - b.visualOrder);
  }
};

export const getAllNodesByTemplate = async (
  ids: string[] | number[]
): Promise<ITreeData[]> => {
  if (ids) {
    const response = (await API.post(`/model/node/get`, {
      templateIds: ids,
      addBody: false,
    })) as ITreeData[];
    return response
      ?.sort((a, b) => a.visualOrder - b.visualOrder)
      ?.filter((_node) => _node.templateId !== COMMENT_TEMPLATE_ID);
  }
};

export const getAllNodes = async (id: string): Promise<ITreeData[]> => {
  if (id) {
    const response = (await API.post(`/model/node/get`, {
      parentIds: id.toString()?.includes(",") ? id.split(",") : [id],
      addBody: false,
    })) as ITreeData[];
    return response
      ?.sort((a, b) => a.visualOrder - b.visualOrder)
      ?.filter((_node) => _node.templateId !== COMMENT_TEMPLATE_ID);
  }
};

export const publishMetamodel = () => {
  return API.post(`/dev/publish`);
};

export const getParentExistence = (id): Promise<boolean> => {
  return API.get(`/model/node/${id}/trash/original-parent/check`);
};

export const getAllNodeDetails = async (id): Promise<INodeDetails[]> => {
  const data = (await API.post(`/model/node/get`, {
    ids: id.toString()?.includes(",") ? id.split(",") : [id],
    addBody: false,
  })) as INodeDetails[];

  return data;
};

export const getNodeDetails = async (id): Promise<INodeDetails> => {
  if (id) {
    const data = (await API.post(`/model/node/get`, {
      ids: id.toString()?.includes(",") ? id.split(",") : [id],
      addBody: true,
    })) as INodeDetails[];
    return data[0];
  }
};

export const getParentNameDetails = async (ids): Promise<INodeDetails[]> => {
  const data = (await API.post(`/model/node/get`, {
    ids: ids.toString()?.includes(",") ? ids.split(",") : [ids],
    addBody: false,
  })) as INodeDetails[];

  return data;
};

export const getHierarchyDetails = (id) => {
  return API.get(`/model/node/${id}/path/get`) as Promise<IHierarchyData>;
};

export const saveVisualOrder = (payload) => {
  return API.patch(`/model/node/visual-order/update`, payload);
};

export const saveParentOrder = (payload) => {
  return API.patch(`/model/node/change-parent`, payload);
};

export const moveNode = (id, currentParentId, newParentId, afterNodeId) => {
  return API.post(`/metamodel/draft/attribute/move`, {
    id: id,
    currentParentId: currentParentId,
    newParentId: newParentId,
    afterNodeId: afterNodeId,
  });
};

export const moveToSelectedNode = (values) => {
  return API.post(`/model/node/trash/restore2position`, {
    parentId: values.parentId,
    nodeIds: values.nodeIds,
  });
};

export const getBitFlags = (): Promise<IBitFlags> => {
  return API.get(`/dev/bitflag`);
};
