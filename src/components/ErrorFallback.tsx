import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

interface ErrorFallbackProps {
  messageKey: string;
  error?: Error | null;
  variant?: "banner" | "inline" | "modal";
  onClose?: () => void;
  accentColor?: string;
  // Control technical details visibility
  showTechnicalDetails?: boolean;
  // Callback for logging errors instead of displaying them
  onErrorLog?: (error: Error) => void;
  // User-friendly alternative to technical details
  helpText?: string;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({
  messageKey,
  error,
  variant = "inline",
  onClose,
  accentColor = "#dc3545",
  // Default to hiding technical details
  showTechnicalDetails = false,
  onErrorLog,
  helpText,
}) => {
  const { t } = useTranslation();
  const [visible, setVisible] = useState(false);
  const [isDetailsExpanded, setIsDetailsExpanded] = useState(false);

  // Calculate derived colors based on the accent color
  const lightAccentColor = `${accentColor}10`;
  const mediumAccentColor = `${accentColor}20`;
  const borderAccentColor = `${accentColor}30`;

  useEffect(() => {
    // Trigger animation after component mount
    const timer = setTimeout(() => setVisible(true), 50);

    // Log error if logging callback is provided
    if (error && onErrorLog) {
      onErrorLog(error);
    }

    return () => clearTimeout(timer);
  }, [error, onErrorLog]);

  const handleClose = () => {
    setVisible(false);
    setTimeout(() => {
      if (onClose) onClose();
    }, 300);
  };

  const toggleDetails = () => {
    setIsDetailsExpanded((prev) => !prev);
  };

  // Base container styles
  const containerStyles: React.CSSProperties = {
    width: "100%",
    maxWidth: "100%",
    margin: variant === "banner" ? "0" : "1rem 0",
    padding: "0",
    boxSizing: "border-box",
    fontFamily:
      'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    transition: "all 0.3s ease",
    opacity: visible ? 1 : 0,
    transform: visible ? "translateY(0)" : "translateY(-10px)",
  };

  // Variant-specific container styles
  const variantContainerStyles: React.CSSProperties =
    variant === "banner"
      ? {
          position: "relative",
          borderRadius: "0",
          borderLeft: "0",
          borderRight: "0",
          borderTop: "0",
          boxShadow: `0 2px 6px ${mediumAccentColor}`,
        }
      : variant === "modal"
      ? {
          position: "relative",
          borderRadius: "8px",
          maxWidth: "500px",
          margin: "1rem auto",
          boxShadow: `0 4px 20px ${mediumAccentColor}`,
        }
      : {
          position: "relative",
          borderRadius: "8px",
        };

  // Error content container
  const errorContentStyles: React.CSSProperties = {
    display: "flex",
    alignItems: "flex-start",
    padding: variant === "banner" ? "1rem 1.5rem" : "1.25rem",
    backgroundColor: lightAccentColor,
    border: `1px solid ${borderAccentColor}`,
    overflow: "hidden",
    ...variantContainerStyles,
  };

  // Left border accent for banner variant
  const bannerAccentStyles: React.CSSProperties =
    variant === "banner"
      ? {
          position: "absolute",
          left: 0,
          top: 0,
          bottom: 0,
          width: "4px",
          backgroundColor: accentColor,
        }
      : {};

  return (
    <div style={containerStyles}>
      <div style={errorContentStyles}>
        {variant === "banner" && <div style={bannerAccentStyles}></div>}

        <div
          style={{
            color: accentColor,
            marginRight: "1rem",
            flexShrink: 0,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z" />
          </svg>
        </div>
        <div
          style={{
            flex: "1",
          }}
        >
          <h2
            style={{
              margin: "0 0 0.5rem 0",
              fontSize: "1.25rem",
              fontWeight: "600",
              color: accentColor,
              lineHeight: "1.2",
            }}
          >
            {t("Error")}
          </h2>
          <p
            style={{
              margin: "0",
              fontSize: "0.95rem",
              color: "#555",
              lineHeight: "1.5",
            }}
          >
            {t(messageKey)}
          </p>

          {/* User-friendly help text if provided */}
          {helpText && (
            <p
              style={{
                margin: "0.75rem 0 0 0",
                fontSize: "0.9rem",
                color: "#666",
                lineHeight: "1.4",
              }}
            >
              {helpText}
            </p>
          )}

          {/* Only show technical details if enabled */}
          {error?.message && showTechnicalDetails && (
            <div
              style={{
                marginTop: "0.75rem",
              }}
            >
              <button
                onClick={toggleDetails}
                style={{
                  display: "flex",
                  alignItems: "center",
                  background: "transparent",
                  border: "none",
                  color: "#666",
                  fontSize: "0.85rem",
                  padding: "4px 8px",
                  cursor: "pointer",
                  borderRadius: "4px",
                  transition: "all 0.2s",
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = "rgba(0,0,0,0.05)";
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = "transparent";
                }}
              >
                <span style={{ marginRight: "6px" }}>
                  {isDetailsExpanded
                    ? t("Hide technical details")
                    : t("Show technical details")}
                </span>
                <svg
                  width="12"
                  height="12"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  style={{
                    transform: isDetailsExpanded
                      ? "rotate(180deg)"
                      : "rotate(0)",
                    transition: "transform 0.2s",
                  }}
                >
                  <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z" />
                </svg>
              </button>

              <div
                style={{
                  height: isDetailsExpanded ? "auto" : "0",
                  overflow: "hidden",
                  opacity: isDetailsExpanded ? 1 : 0,
                  transition: "all 0.3s ease",
                  marginTop: isDetailsExpanded ? "0.75rem" : "0",
                  maxHeight: isDetailsExpanded ? "200px" : "0",
                  overflowY: "auto",
                }}
              >
                <div
                  style={{
                    padding: "0.75rem",
                    backgroundColor: mediumAccentColor,
                    borderRadius: "4px",
                  }}
                >
                  <pre
                    style={{
                      margin: "0",
                      fontSize: "0.85rem",
                      color: accentColor,
                      whiteSpace: "pre-wrap",
                      wordBreak: "break-word",
                      fontFamily:
                        'SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
                    }}
                  >
                    {error.message}
                  </pre>
                </div>
              </div>
            </div>
          )}
        </div>
        {onClose && (
          <button
            onClick={handleClose}
            style={{
              background: "transparent",
              border: "none",
              color: "#888",
              cursor: "pointer",
              padding: "4px",
              marginLeft: "8px",
              flexShrink: 0,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              outline: "none",
              transition: "all 0.2s",
              borderRadius: "50%",
              width: "24px",
              height: "24px",
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.color = accentColor;
              e.currentTarget.style.backgroundColor = mediumAccentColor;
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.color = "#888";
              e.currentTarget.style.backgroundColor = "transparent";
            }}
            aria-label={t("Close")}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
            </svg>
          </button>
        )}
      </div>
    </div>
  );
};

export { ErrorFallback };
