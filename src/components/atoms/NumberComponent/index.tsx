import { Input } from "antd";
import { ReactNode, Ref, forwardRef, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  MAX_SUPPORTED_POSITIVE_VALUE,
  MIN_SUPPORTED_NEGATIVE_VALUE,
} from "../../../constants";

interface Props {
  status?: "error" | "";
  onChange: any;
  value: any;
  decimal?: boolean;
  addonAfter?: ReactNode;
  min?: number;
}

const NumberComponent = forwardRef(
  (
    { status, onChange, value, decimal, addonAfter, min }: Props,
    ref: Ref<any>
  ) => {
    const [error, setError] = useState(null);
    const { t } = useTranslation();

    // const allowedKeys = ["Backspace", "Enter", "ArrowRight", "ArrowLeft"];

    return (
      <>
        <Input
          status={`${(status || !!error) && "error"}`}
          ref={ref}
          onChange={(e) => {
            const newValue = e.target.value;

            setError(null);
            onChange(newValue);
            if (decimal) {
              if (newValue?.includes(".")) {
                const decimalPart = newValue.split(".")[1];
                if (decimalPart && decimalPart.length > 12) {
                  setError(
                    t("The value cannot have more than 12 decimal places")
                  );
                }
              }
            } else {
              if (
                BigInt(newValue) > MAX_SUPPORTED_POSITIVE_VALUE ||
                BigInt(newValue) < MIN_SUPPORTED_NEGATIVE_VALUE
              ) {
                setError(t("Number too long!"));
              }
            }
            // for checking max 12 precision for decimal fields
          }}
          value={value}
          min={min || null}
          onKeyDown={(event) => {
            // Only block invalid printable characters
            if (event.key.length === 1) {
              if (!/[0-9]/.test(event.key) && !(decimal && event.key === ".")) {
                event.preventDefault();
                setError(
                  t("The value of this attribute must be an ", {
                    type: decimal ? t("decimal") : t("integer"),
                  })
                );
              }
            }
            // All other keys (arrows, function keys, modifiers, etc.) are allowed
          }}
          onPaste={(event) => {
            const pasteData = event.clipboardData.getData("Text");

            // Allow only numbers and decimal if applicable
            const regex = decimal ? /^[0-9]*\.?[0-9]*$/ : /^[0-9]*$/;

            if (!regex.test(pasteData)) {
              event.preventDefault();
              setError(
                t("The value of this attribute must be an ", {
                  type: decimal ? t("decimal") : t("integer"),
                })
              );
            }
          }}
          addonAfter={addonAfter}
        />
        {!!error && <p className="error">{error}</p>}
      </>
    );
  }
);

export { NumberComponent };
