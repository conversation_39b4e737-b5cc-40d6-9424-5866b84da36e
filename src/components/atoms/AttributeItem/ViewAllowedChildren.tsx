import { DetailsContainer, MyTable } from "../../organisms";
import { useMemo, useState } from "react";
import { Dropdown, Flex } from "antd";
import { useTranslation } from "react-i18next";
import { styled } from "@linaria/react";
import { useHyperlinkActions } from "../../../utils/functions/customHooks";
import { IAllowedChildrens } from "../../../interfaces";
import { Checkbox } from "primereact/checkbox";

const baseUrl =
  import.meta.env.VITE_APP_BASE_URL === "/"
    ? ""
    : import.meta.env.VITE_APP_BASE_URL;

interface Props {
  value: IAllowedChildrens[];
  className?: string;
}

const ViewAllowedChildren = ({ value, className }: Props) => {
  const { t } = useTranslation();

  const [isDetailsOpen, setDetailsOpen] = useState(null);

  const { handleHyperlinkAction, handleTrashHyperlinkClick } =
    useHyperlinkActions();

  const HYPERLINKS_ACTIONS_TRASH = [
    {
      label: t("View Details"),
      key: "details",
    },
    {
      label: t("View in trashcan"),
      key: "view-in-trashcan",
    },
  ];

  const HYPERLINKS_ACTIONS = [
    {
      label: t("View Details"),
      key: "details",
    },
    {
      label: t("Open in new tab"),
      key: "open-in-new-tab",
    },
  ];

  const handleNodeClick = async (key, id, name) => {
    switch (key) {
      case "details": {
        setDetailsOpen({ id: id, name: name });
        return;
      }
      case "open-in-new-tab": {
        // const parentID = await getParentID(id);
        window.open(
          `${window.origin}${baseUrl}/settings/metamodel/-1?nodeId=${id}`
        );
        return;
      }

      case "view-in-trashcan": {
        handleTrashHyperlinkClick(id);
      }
    }
  };

  const COLUMNS = useMemo(() => {
    return [
      {
        headerName: "Name",
        field: "name",
        flex: 1,
        cellRenderer: ({ data: record }) => {
          return (
            <Flex gap={8} align="center">
              <Checkbox className="readonly-checkbox" checked readOnly />
              <Dropdown
                menu={{
                  items: record.inTrash
                    ? HYPERLINKS_ACTIONS_TRASH
                    : HYPERLINKS_ACTIONS,
                  onClick: (e) =>
                    handleNodeClick(e.key, record.id, record.name),
                }}
                trigger={["contextMenu"]}
              >
                <p
                  className={`title-container ${
                    record.inTrash ? "trash-hyperlink" : ""
                  }`}
                  onClick={async (e) => {
                    e.stopPropagation();
                    handleHyperlinkAction({
                      id: record.id,
                      inTrash: record.inTrash,
                    });
                  }}
                >
                  {record.name}
                </p>
              </Dropdown>
            </Flex>
          );
        },
      },
    ];
  }, [value]);

  return (
    <Wrapper className={className}>
      <MyTable
        excelFileName="hyperlinks"
        noHeader
        noSelect
        columns={COLUMNS}
        data={value}
        // triggerChange={triggerChange}
        noDownload
        // selected={data?.filter((item) =>
        //   value?.map((obj) => obj.id).includes(item.id)
        // )}
      />

      {!!isDetailsOpen && (
        <DetailsContainer
          id={isDetailsOpen.id}
          isOpen={!!isDetailsOpen}
          onClose={() => setDetailsOpen(null)}
          title={isDetailsOpen.name}
        />
      )}
    </Wrapper>
  );
};

export { ViewAllowedChildren };

const Wrapper = styled.div`
  & .title-container {
    color: var(--color-text);
  }
  & .ant-dropdown-trigger {
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }
`;
