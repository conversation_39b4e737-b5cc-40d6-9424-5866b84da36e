import { styled } from "@linaria/react";
import { <PERSON><PERSON>, Empty, Toolt<PERSON> } from "antd";
import { Dialog } from "primereact/dialog";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { useEffect, useState } from "react";
import {
  ATTRIBUTES_WITH_NO_MANDATORY,
  GET_ALL_COMMENTS,
  GET_COMMENTS,
  MAX_SUPPORTED_POSITIVE_VALUE,
  METAATTRIBUTE_ID_DISPLAY_OPTIONS,
  METAATTRIBUTE_ID_ISMANDATORY,
  MIN_SUPPORTED_NEGATIVE_VALUE,
  COMMENT_TEMPLATE_ID,
  COMMENT_AUTHOR_ID,
  COMMENT_DATE_ID,
  GET_COUNTERS,
  GET_BITFLAGS,
} from "../../../constants";
import { useMutation, useQueryClient } from "react-query";
import { addNodeService } from "../../../services/node";
import {
  useNotification,
  useFlags,
} from "../../../utils/functions/customHooks";
import { useSearchParams } from "react-router-dom";
import { I_MULTIPLICITY, IAttributes, IBitFlags } from "../../../interfaces";
import { AttributeItem } from "../AttributeItem";
import { getAttributeTitleWidth } from "../../../utils";
import { LoadingOutlined } from "@ant-design/icons";
import dayjs from "dayjs";

interface Props {
  visible: boolean;
  onHide: () => void;
  onSuccess: () => void;
  edit?: boolean;
}

const AddComment = ({ visible, onHide, onSuccess }: Props) => {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const queryClient = useQueryClient();
  const { getFlags } = useFlags();

  const [loading, setLoading] = useState(true);
  const [disabled, setDisabled] = useState(false);
  const [disabledInfo, setDisabledInfo] = useState("");
  const [mandatoryAttributes, setMandatoryAttributes] = useState([]);
  const [editingAttribute, setEditingAttribute] = useState(null);
  const [attributes, setAttributes] = useState([]);

  const { contextHolder, showErrorNotification } = useNotification();

  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );

  const { userInfo, globalPermissions } = useSelector(
    (root: RootState) => root.auth
  );

  // node name should be sent by frontend
  const generateUniqueName = () =>
    `comment_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

  // check for mandatory attributes
  const isMandatoryMultiplicity = (attribute) => {
    if (!ATTRIBUTES_WITH_NO_MANDATORY.includes(attribute.type)) {
      return attribute?.mandatory;
    }

    // checking multiplicity for relation and compound
    if (attribute.type === "relation") {
      const multiplicity = attribute.multiplicity?.split("..")[0];
      if (!["0", "n"].includes(multiplicity)) {
        return true;
      }
    } else if (
      attribute.type === "compound" ||
      attribute.type === "compoundSimple" ||
      attribute.type === "roleMatrix"
    ) {
      const multiplicityOfList1 = attribute.multiplicityList1?.split("..")[0];
      const multiplicityOfList2 = attribute.multiplicityList2?.split("..")[0];
      if (!["0", "n"].includes(multiplicityOfList1)) {
        return true;
      }
      if (!["0", "n"].includes(multiplicityOfList2)) {
        return true;
      }
    }
    return false;
  };

  const generateValue = (id, type, value, defaultValue) => {
    if (type === I_MULTIPLICITY) {
      return {
        text1: value?.split("..")[0],
        text2: value?.split("..")[1],
      };
    } else if (id === METAATTRIBUTE_ID_ISMANDATORY) return false;
    else if (type === "switch") {
      return value || defaultValue || false;
    }
    return defaultValue || null;
  };

  useEffect(() => {
    setDisabledInfo("");
    let disabled = false;
    attributes.forEach((item: any) => {
      // check for empty values
      const isEmpty =
        (item?.type === "switch" && item?.value === null) ||
        !item?.value ||
        item?.value?.length === 0 ||
        (item?.type === "dropdownItems" && (item?.value || []).length === 0);

      // check for invalid regex
      const invalidRegex =
        item?.regex && !new RegExp(item?.regex).test(item?.value);

      if ((mandatoryAttributes.includes(item.id) && isEmpty) || invalidRegex) {
        disabled = true;
      }

      // check for multiplicity mismatch

      if (
        item?.type === "compound" ||
        item?.type === "compoundSimple" ||
        item?.type === "roleMatrix"
      ) {
        const multiplicityList1 = item?.multiplicityList1?.split("..");
        const valueLength = item?.value ? item?.value?.length : 0;

        if (multiplicityList1?.length > 0) {
          const min = multiplicityList1[0];
          const max = multiplicityList1[1];

          if (min === "n" && max === "n") {
            // do nothing
          } else if (max === "n") {
            if (!(valueLength >= min)) {
              disabled = true;
            }
          } else if (!(valueLength >= min && valueLength <= max)) {
            disabled = true;
          }
        }
      }

      if (item?.type === "relation" || item?.type === "multipleSelect") {
        const value =
          item?.type === "relation"
            ? item?.value || []
            : Object.keys(item?.value || {});

        const multiplicity = item?.multiplicity?.split("..");

        if (multiplicity?.length > 0) {
          const min = multiplicity[0];
          const max = multiplicity[1];

          if (min === "n" && max === "n") {
            // do nothing
          } else if (max === "n") {
            if (!(value?.length >= min)) disabled = true;
          } else if (!(value?.length >= min && value?.length <= max)) {
            disabled = true;
            setDisabledInfo(`Invalid multiplicity for ${item?.name}`);
          }
        }
      }

      // check for supported values
      if (item?.type === "number" && item?.value) {
        if (
          BigInt(item?.value) > MAX_SUPPORTED_POSITIVE_VALUE ||
          BigInt(item?.value) < MIN_SUPPORTED_NEGATIVE_VALUE
        ) {
          disabled = true;
        }
      }

      if (item?.type === "decimal" && item?.value) {
        if (item?.value?.toString()?.includes(".")) {
          const decimalPart = item?.value?.toString()?.split(".")[1];
          if (decimalPart?.length === 0) {
            disabled = true;
            setDisabledInfo("Invalid decimal number!");
          }
          if (decimalPart && decimalPart.length > 12) {
            disabled = true;
          }
        } else {
          disabled = true;
          setDisabledInfo("Invalid decimal number!");
        }
      }
    });
    setDisabled(disabled);
  }, [attributes, templatesData]);

  // dynamic calculation of attribute title width
  const calculateWidth = () => {
    const titles = document.querySelectorAll(".comment-attribute-title") as any;

    titles.forEach((title) => {
      title.style.width = `fit-content`;
    });
    const maxTitleWidth = getAttributeTitleWidth(".comment-attribute-title");
    titles.forEach((title) => {
      title.style.width = `${maxTitleWidth}px`;
    });
  };

  useEffect(() => {
    calculateWidth();

    setTimeout(() => {
      calculateWidth();
    }, 300);
  }, [attributes, editingAttribute]);

  // load from templates
  const loadAttributes = async () => {
    const selectedTemplate = templatesData[COMMENT_TEMPLATE_ID];

    if (selectedTemplate) {
      const mandatoryAttributes = [] as number[];
      const attributeTemplates = [];

      selectedTemplate?.attributeTemplates?.forEach(
        (attribute: IAttributes, index: number) => {
          if ([COMMENT_AUTHOR_ID, COMMENT_DATE_ID].includes(attribute.id)) {
            return;
          }
          const newAttribute = { ...attribute };
          if (isMandatoryMultiplicity(attribute)) {
            mandatoryAttributes.push(attribute.id);
          }
          newAttribute.id = attribute.id || index + 1;
          newAttribute.mandatory = isMandatoryMultiplicity(attribute);
          newAttribute.value = generateValue(
            attribute.id,
            attribute.type,
            attribute.value,
            attribute?.defaultValue?.value
          );
          newAttribute["touched"] = false;
          newAttribute["draft"] = true;
          attributeTemplates.push(newAttribute);
        }
      );

      setMandatoryAttributes([...mandatoryAttributes]);
      setAttributes([...attributeTemplates]);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (!templatesData) {
      return;
    }
    setLoading(true);
    loadAttributes();
  }, [templatesData]);

  const addNodeMutation = useMutation(addNodeService, {
    onSuccess: () => {
      queryClient.invalidateQueries([GET_COMMENTS, searchParams.get("nodeId")]); // node-related comments
      queryClient.invalidateQueries([GET_COUNTERS, searchParams.get("nodeId")]); // counters
      queryClient.invalidateQueries(GET_ALL_COMMENTS); //comments
      onSuccess();
      onHide();
    },
    onError: () => {
      showErrorNotification(t("Please try again after sometimes"));
    },
  });

  const handleSave = () => {
    let allAttributes = [...attributes];

    // Get the bitFlags from the query cache
    const bitFlags = queryClient.getQueryData(GET_BITFLAGS) as IBitFlags;

    // Create a map of attribute bitFlags
    const attributeBitFlags = {};

    const selectedAttributeTemplates =
      templatesData[COMMENT_TEMPLATE_ID]?.attributeTemplates;

    // For each attribute template, store its bitFlag if it has one
    selectedAttributeTemplates?.forEach((attr) => {
      attributeBitFlags[attr.id] = attr.bitFlag;
    });

    allAttributes = allAttributes.filter((attr) => {
      if (attr.bitFlag !== undefined) {
        const flags = getFlags(attr.bitFlag);
        return !flags.includes("EDIT_VALUE_OFF");
      }
      return true;
    });

    if (
      selectedAttributeTemplates?.find((attr) => attr.id === COMMENT_AUTHOR_ID)
    ) {
      const AUTHOR = {
        ...selectedAttributeTemplates?.find(
          (attr) => attr.id === COMMENT_AUTHOR_ID
        ),
      };

      AUTHOR.value = [
        {
          id: userInfo?.id,
          attributeLookup: null,
          inTrash: false,
          name: userInfo?.name,
          templateHasAttributes: userInfo?.body?.length > 0,
          pathName: userInfo?.pathName,
          templateId: userInfo?.templateId,
        },
      ];
      allAttributes.push(AUTHOR);
    }

    if (
      selectedAttributeTemplates?.find((attr) => attr.id === COMMENT_DATE_ID)
    ) {
      const DATE = {
        ...selectedAttributeTemplates?.find(
          (attr) => attr.id === COMMENT_DATE_ID
        ),
      };
      DATE.value = dayjs(new Date()).format("YYYY-MM-DDTHH:mm");
      allAttributes.push(DATE);
    }

    allAttributes.forEach((attr) => {
      if (attr.type === "relation") {
        attr.value = attr.value || [];
      }

      if (
        attr.type?.startsWith("compound") ||
        attr.type?.startsWith("roleMatrix")
      ) {
        attr.value = attr.value ? attr.value : [];
      }

      // cleanup
      delete attr.mandatory;
      delete attr.help;
      delete attr.order;
      delete attr.items;
      delete attr.regex;
      delete attr.dictionary;
      delete attr.filterMenu;
      delete attr.filterTemplate;
      delete attr.multiplicityList1;
      delete attr.multiplicityList2;
      delete attr.nameList1;
      delete attr.nameList2;
    });

    addNodeMutation.mutate({
      name: generateUniqueName(),
      id: searchParams.get("nodeId"), //parentId
      templateId: COMMENT_TEMPLATE_ID,
      nodeType: "DATA",
      body: allAttributes,
      bitFlags: bitFlags,
      attributeBitFlags: attributeBitFlags,
    });
  };

  const handleEdit = (id, value, type) => {
    const allAttributes = [...attributes];

    if (type === "dropdownItems") {
      let valueIndex;
      // for single select
      valueIndex = allAttributes.findIndex((item) => item.type === "select");
      const findIndex = value?.find(
        (item) => item.value === allAttributes[valueIndex]?.value
      );
      if (!findIndex || findIndex === -1) {
        allAttributes[valueIndex].value = "";
      }

      // for multi-select

      valueIndex = allAttributes.findIndex(
        (item) => item.type === "multipleSelect"
      );
      const multipleItems = [];
      allAttributes[valueIndex]?.value?.forEach((multiSelect) => {
        const findIndex = value?.find((item) => item.value === multiSelect);
        if (findIndex) {
          multipleItems.push(multiSelect);
        }
      });
      allAttributes[valueIndex].value = multipleItems;
    }

    const findIndex = allAttributes.findIndex((item) => item.id === id);
    allAttributes[findIndex].value = value;
    setAttributes([...allAttributes]);
  };

  // for selection options
  const getSelectItems = (id, items, type, attributeId) => {
    if (id === METAATTRIBUTE_ID_DISPLAY_OPTIONS) {
      return [
        { value: "hierarchy", label: t("Hierarchy") },
        { value: "tabular", label: t("Tabular") },
      ];
    }

    if (type === "lifecycle") {
      return attributeId;
    }
    if (items && type === "relation") {
      return items;
    }
    if (items) {
      const dropdownItems = [];
      Object.keys(items).map((item) => {
        dropdownItems.push({ label: items[item], value: item });
      });
      return dropdownItems;
    }
    return [];
  };

  return (
    <Dialog
      header={t("Add comment")}
      visible={visible}
      onHide={onHide}
      style={{ width: "50vw", height: "40vw" }}
      className="export-modal draggable-modal"
    >
      <Wrapper>
        {contextHolder}
        {loading ? (
          <div className="loader" key={`skeleton`}>
            <LoadingOutlined />
          </div>
        ) : attributes.length === 0 ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={t("No attributes")}
          />
        ) : (
          <div className="attribute-wrapper" id="workspace-area">
            {attributes.map((item) => {
              return (
                <AttributeItem
                  key={item.id}
                  {...item}
                  onEdit={(value) => handleEdit(item.id, value, item.name)}
                  title={item.name}
                  dropdownItems={getSelectItems(
                    item?.id,
                    item?.allowedLinksValues || item?.items,
                    item?.type,
                    item?.id
                  )}
                  isEditing={editingAttribute === item.id}
                  onEditClick={(editingAttribute) => {
                    if (editingAttribute) {
                      const allAttributes = [...attributes];
                      const selectedAttributeIndex = allAttributes?.findIndex(
                        (attr) => attr.id === editingAttribute
                      );

                      const selectedAttribute =
                        allAttributes[selectedAttributeIndex];

                      if (
                        selectedAttribute?.draft &&
                        selectedAttribute?.defaultValue?.value &&
                        !selectedAttribute?.touched
                      ) {
                        selectedAttribute.value = null;
                      }
                      selectedAttribute.touched = true;
                      setAttributes([...allAttributes]);
                    }
                    setEditingAttribute(editingAttribute);
                  }}
                  titleClassName={"comment-attribute-title"}
                  readOnly={!globalPermissions.includes("EDIT")}
                />
              );
            })}
          </div>
        )}

        <div className="buttons">
          <Tooltip title={t(disabledInfo)} placement="bottomLeft">
            <Button
              type="primary"
              onClick={handleSave}
              disabled={disabled}
              loading={addNodeMutation.isLoading}
            >
              {t("Add")}
            </Button>
          </Tooltip>
        </div>
      </Wrapper>
    </Dialog>
  );
};

export { AddComment };

const Wrapper = styled.div`
  padding-bottom: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;

  & .attribute-wrapper {
    flex: 1;
    display: flex;
    flex-wrap: nowrap;
    overflow: auto;
    flex-direction: column;
    justify-content: flex-start;
  }

  & .loader {
    font-size: 30px;
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: center;
    margin-top: 30px;
  }

  & button:disabled {
    background-color: var(--color-text);
    color: #fff;
    opacity: 0.7;
  }

  & .buttons {
    display: flex;
    justify-content: right;
    margin-top: 20px;

    & button {
      font-size: 13px;
      box-shadow: none;
    }
  }
`;
