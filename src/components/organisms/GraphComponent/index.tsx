import { styled } from "@linaria/react";
import { useEffect, useState } from "react";
import { useQuery } from "react-query";
import { getGraphDatas } from "../../../services";
import {
  GET_GRAPH_DATA,
  getAttributeFile,
  getAttributeIcon,
} from "../../../constants";
import { CytoscapeWithContextMenu } from "../CytoscapeWithContextMenu";
import { IGraphData, IGraphEdges } from "../../../interfaces";
import { useSearchParams } from "react-router-dom";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { useTheme } from "../../../utils";
import { LoadingOutlined } from "@ant-design/icons";
import { withErrorBoundary } from "../../withErrorBoundary";

const GraphComponentBase = ({ id, fromTrashcan }) => {
  const [graphData, setGraphData] = useState([]);
  const [key, setKey] = useState(0);
  const theme = useTheme() as any;
  const [filterOptions, setFilterOptions] = useState({
    templates: [] as any[],
    filters: [],
    display: [],
  });
  const [selectedTemplateId, setSelectedTemplateId] = useState(null);
  const [allTemplates, setAllTemplates] = useState([]);
  const [nodeId, setNodeId] = useState(null);

  const [selectedFilters, setSelectedFilters] = useState({
    templates: null,
    edges: [],
    filters: ["display-all"],
    display: ["hierarchy", "object-names", "relations-name", "orphans"],
    appliedFilter: [],
  });

  useEffect(() => {
    setNodeId(id);
    setGraphData([]);
  }, [id]);

  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );

  const identifyHyperlinkNodes = (data: IGraphData, parentId) => {
    const hyperlinkNodes = [];
    data.edges?.forEach((edge) => {
      if (["RELATION", "COMPOUND"].includes(edge.edgeType)) {
        if (edge.nodeTo != parentId) {
          hyperlinkNodes.push(edge.nodeTo);
        }

        if (edge.nodeFrom != parentId) {
          hyperlinkNodes.push(edge.nodeFrom);
        }
      }
    });
    return hyperlinkNodes;
  };

  const getParentID = (nodeId, edges: IGraphEdges[]) => {
    let parentId = null;
    edges?.forEach((edge) => {
      if (edge.edgeType === "CHILDOF" && edge.nodeTo == nodeId) {
        parentId = edge.nodeFrom;
      }
    });

    return parentId;
  };

  const [searchParams] = useSearchParams();

  const { isFetching } = useQuery<any>(
    [GET_GRAPH_DATA, nodeId],
    () => getGraphDatas(nodeId),
    {
      enabled: !!nodeId && !!templatesData && !searchParams.get("draft"),
      onSuccess: (data) => {
        setGraphData([]);
        const graphNodes = [];
        const graphEdges = [];
        const graphTemplates = [];
        const selectedTemplate = [];
        const allTemplateIds = [];

        const hyperlinksNodeIds = [
          ...identifyHyperlinkNodes(data, nodeId),
          Number(nodeId),
        ];

        if (hyperlinksNodeIds.length > 1) {
          const parentNode = data?.nodes?.find((node) => node.id == nodeId);
          if (parentNode) {
            graphNodes.push({
              group: "nodes",
              data: {
                id: `group-links-${nodeId}`,
                name: "group1",
                icon: null,
                label: "",
              },
            });
          }
        }

        data?.nodes?.forEach((child) => {
          if (hyperlinksNodeIds.includes(child.id)) {
            const attributeId = data.edges.find(
              (edge) => edge.nodeTo === child.id || edge.nodeFrom === child.id
            )?.attributeId;
            const template = templatesData[Number(child.templateId)];

            if (template) {
              if (
                graphTemplates.findIndex(
                  (item) => item?.id === child.templateId
                ) === -1
              ) {
                allTemplateIds.push(child.templateId);
                selectedTemplate.push(child.templateId);
                graphTemplates.push({
                  id: child.templateId,
                  title: template.name,
                  key: child.templateId,
                  icon: getAttributeIcon(template?.icon || "_30_folder"),
                  children: [],
                });
              }
            }
            if (child.id == nodeId) {
              setSelectedTemplateId(child.templateId);
            }

            graphNodes.push({
              group: "nodes",
              data: {
                id: child.id.toString(),
                label: child.name,
                icon: getAttributeFile(template?.icon || "_30_folder"),
                hasChildren: child.hasChildren,
                hasParent: child.hasParent,
                hasRelations: child.hasRelations,
                template: template?.name,
                inTrash: child?.inTrash,
                textColor: child?.inTrash
                  ? theme.trashBreadcrumbsColor
                  : "rgba(8,67,117,1)",
                templateId: child.templateId,
                connectedNode: nodeId,
                parentId: child?.parentId || getParentID(child.id, data?.edges),
                nodeType: child.id == nodeId ? "child" : "link",
                parent: child.id == nodeId ? null : `group-links-${nodeId}`,
                attributeId: attributeId,
                isExpanded:
                  child.id == nodeId && hyperlinksNodeIds.length > 0
                    ? true
                    : undefined,
                isChildrensShown: false,
              },
            });
          }
        });

        data?.edges?.forEach((edge) => {
          if (["RELATION", "COMPOUND"].includes(edge.edgeType)) {
            const templateId = data.nodes.find(
              (node) => node.id === edge.nodeFrom
            )?.templateId;

            const parentTemplate = graphTemplates.find(
              (template) => template.id === templateId
            );

            if (
              !parentTemplate?.children?.some(
                (child) => child.attributeId === edge.attributeId
              )
            ) {
              selectedTemplate.push(edge.attributeId);
              parentTemplate.children.push({
                id: edge.attributeId,
                attributeId: edge.attributeId,
                key: edge.attributeId,
                title: edge.relationName,
                isEdge: true,
              });
            }

            graphEdges.push({
              data: {
                id: `edge-${edge?.id}`,
                source: edge.nodeFrom.toString(),
                target: edge?.nodeTo.toString(),
                label: edge?.relationName,
                attributeId: edge.attributeId,
                color: "green",
                arrow: "none",
                parentId: nodeId,
                templateId: templateId,
              },
            });
          }
        });
        setFilterOptions({ ...filterOptions, templates: graphTemplates });
        setSelectedFilters({ ...selectedFilters, templates: selectedTemplate });
        setAllTemplates(allTemplateIds);
        setKey(key + 1);
        setGraphData([...graphNodes, ...graphEdges]);
      },
    }
  );

  return (
    <Content>
      {isFetching ? (
        <div className="loader">
          <LoadingOutlined />
        </div>
      ) : (
        <CytoscapeWithContextMenu
          key={key}
          nodeId={nodeId}
          elements={graphData}
          displayOptions={selectedFilters.display}
          filterOptions={filterOptions}
          setFilterOptions={setFilterOptions}
          selectedFilters={selectedFilters}
          setSelectedFilters={setSelectedFilters}
          allTemplates={allTemplates}
          setAllTemplates={setAllTemplates}
          selectedTemplateId={selectedTemplateId}
          setSelectedTemplateId={setSelectedTemplateId}
          fromTrashcan={fromTrashcan}
        />
      )}
    </Content>
  );
};

export const GraphComponent = withErrorBoundary(
  GraphComponentBase,
  "error.generic"
);

const Content = styled.div`
  & .loader {
    font-size: 36px;
    display: flex;
    width: 100%;
    justify-content: center;
  }
  flex: 1;
  overflow: hidden;
  justify-content: flex-end;
  display: flex;

  background-color: #fff;
`;
