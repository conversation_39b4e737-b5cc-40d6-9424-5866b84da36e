import { useSelector } from "react-redux";
import { MyTable } from "../MyTable";
import { RootState } from "../../../store";
import React, { useEffect, useRef, useState } from "react";
import { getParentID, transformObjectPath } from "../../../utils";
import { getAllNodesByTemplate } from "../../../services/node";
import {
  NODES_MENU_ITEMS,
  PERMISSION_PERSON_ID,
  PERSON_AD_ID,
  TRASH_NODES_MENU_ITEMS,
} from "../../../constants";
import { Dropdown, Flex } from "antd";
import { useHyperlinkActions } from "../../../utils/functions/customHooks";
import { DetailsContainer } from "../DetailsContainer";
import { DELETED_FLAG } from "../../../interfaces";
import { withErrorBoundary } from "../../withErrorBoundary";

const baseUrl =
  import.meta.env.VITE_APP_BASE_URL === "/"
    ? ""
    : import.meta.env.VITE_APP_BASE_URL;

function SelectUserTableBase({ onSelect, defaultSelected }) {
  const [loading, setLoading] = useState(true);
  const [rows, setRows] = useState([]);
  const [isDetailsOpen, setDetailsOpen] = useState(null);
  const [initialSelected, setInitialSelected] = useState(null);
  const defaultInitialized = useRef(false);

  const { handleHyperlinkAction, handleTrashHyperlinkClick } =
    useHyperlinkActions();

  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );

  useEffect(() => {
    if (!defaultInitialized.current) {
      defaultInitialized.current = true;
      setInitialSelected(defaultSelected);
    }
  }, [defaultSelected, rows]);

  const COLUMNS = [
    {
      headerName: "Osoba",
      field: "name",
      minWidth: 250,
      flex: 1,
      cellRenderer: "agGroupCellRenderer",
      cellRendererParams: {
        innerRenderer: ({ data: record }) => {
          return (
            <Flex vertical gap={8}>
              <Dropdown
                menu={{
                  items: record?.inTrash
                    ? TRASH_NODES_MENU_ITEMS
                    : NODES_MENU_ITEMS,
                  onClick: (e) =>
                    handleNodeClick(e.key, record.id, record.name),
                }}
                trigger={["contextMenu"]}
              >
                <p
                  className={`title-container title ${
                    record?.inTrash ? "trash-hyperlink" : ""
                  }`}
                  onClick={async (e) => {
                    e.stopPropagation();
                    handleHyperlinkAction({
                      id: record.id,
                      inTrash: record?.inTrash,
                    });
                  }}
                >
                  {record?.name}
                </p>
              </Dropdown>
            </Flex>
          );
        },
      },
    },
    {
      headerName: "Typ",
      field: "type",
      minWidth: 250,
      flex: 1,
    },

    {
      headerName: "Path",
      field: "pathName",
      minWidth: 200,
      flex: 1,
      cellRenderer: ({ data }) => (
        <p className="right-align">
          {data?.pathName
            ? transformObjectPath(data?.pathName, data?.inTrash)
            : "-"}
        </p>
      ),
    },
  ];

  const handleNodeClick = async (key, id, name) => {
    switch (key) {
      case "details": {
        setDetailsOpen({ id: id, name: name });
        return;
      }
      case "open-in-new-tab": {
        const parentID = await getParentID(id);
        window.open(
          `${window.origin}${baseUrl}/details/${parentID}?nodeId=${id}`
        );
        return;
      }

      case "view-in-trashcan": {
        handleTrashHyperlinkClick(id);
      }
    }
  };

  const generateRows = async () => {
    const response = await getAllNodesByTemplate([
      PERMISSION_PERSON_ID,
      PERSON_AD_ID,
    ]);
    const rows = [];
    response?.forEach((node) => {
      rows.push({
        id: node?.id,
        name: node?.name,
        templateId: node?.templateId,
        inTrash: node.flag?.includes(DELETED_FLAG),
        pathName: node.pathName,
        templateHasAttributes:
          templatesData[node?.templateId]?.attributeTemplates?.length > 0,
        permissionsId: node?.permissionsId,
        type: node?.templateId === PERMISSION_PERSON_ID ? "Osoba" : "Osoba AD",
      });
    });
    setRows(rows);
    setLoading(false);
  };

  useEffect(() => {
    generateRows();
  }, [templatesData]);

  return (
    <div>
      <MyTable
        onSelect={onSelect}
        noHeader
        loading={loading}
        defaultSelected={initialSelected}
        data={rows}
        columns={COLUMNS}
      />

      {!!isDetailsOpen && (
        <DetailsContainer
          id={isDetailsOpen.id}
          isOpen={!!isDetailsOpen}
          onClose={() => setDetailsOpen(null)}
          title={isDetailsOpen.name}
        />
      )}
    </div>
  );
}

export const SelectUserTable = withErrorBoundary(
  React.memo(SelectUserTableBase),
  "error.generic"
);
