import React, { useEffect, useRef, useState } from "react";
import "cytoscape-context-menus/cytoscape-context-menus.css";
import cytoscape from "cytoscape";
import contextMenus from "cytoscape-context-menus";
import { DetailsContainer } from "../DetailsContainer";
import { useTranslation } from "react-i18next";
import { getHierarchyDetails } from "../../../services/node";
import { getGraphDatas } from "../../../services";
import { useQueryClient } from "react-query";
import {
  GET_GRAPH_DATA,
  getAttributeFile,
  getAttributeIcon,
  VIEWPORT_KEY,
} from "../../../constants";
import cose from "cytoscape-fcose";
import i18next from "i18next";
import { IGraphData, IGraphNodes } from "../../../interfaces";
import {
  useHyperlinkActions,
  useNotification,
} from "../../../utils/functions/customHooks";
import { GraphFilterTree } from "../GraphComponent/GraphFilterTree";
import { DisplayFilters } from "../GraphComponent/DisplayFilters";
import { TemplatesFilter } from "../GraphComponent/TemplatesFilter";
import { styled } from "@linaria/react";
import { layoutOptions } from "./layoutOptions";
import { ICytoscape } from "./interfaces";
import {
  doNodesWithTemplateIdExist,
  doesEdgeExist,
  doesEdgeToParentExist,
  getEdgesBetween,
  identifyChildNodes,
  identifyHyperlinkNodes,
  toggleOrphanFilter,
} from "./functions";
import {
  collapseNodeRecursively,
  hideChildrenRecursively,
} from "./graphActions";
import { graphStyles } from "./style";
import { ResizableDiv } from "../../molecules";
import Tooltip from "./Tooltip";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { useTheme } from "../../../utils";
import { LoadingOutlined } from "@ant-design/icons";

const CytoscapeWithContextMenu: React.FC<ICytoscape> = ({
  elements,
  displayOptions,
  selectedFilters,
  setSelectedFilters,
  nodeId,
  filterOptions,
  setFilterOptions,
  allTemplates,
  setAllTemplates,
  setSelectedTemplateId,
  selectedTemplateId,
  fromTrashcan,
}) => {
  const extensionsRegistered = useRef(false);
  const theme = useTheme() as any;
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [loading, setLoading] = useState(false);
  const [tooltip, setTooltip] = useState({
    display: false,
    x: 0,
    y: 0,
    text: "",
  });

  const { handleTrashHyperlinkClick } = useHyperlinkActions();
  const { showErrorNotification, contextHolder, showInfoNotification } =
    useNotification();

  const [cy, setCy] = useState(null);
  const [selectedNodeId, setSelectedNodeId] = useState(null);
  const [selectedNodeForDetails, setSelectedNodeForDetails] = useState(null);
  const [isTemplateUpdated, setTemplateUpdated] = useState(false);

  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );

  useEffect(() => {
    setTemplateUpdated(false);
  }, [location.pathname, nodeId]);

  const expandOrShowChildren = async (
    cytoscapeInstance,
    event,
    action: "expand" | "show-children" | "show-parent"
  ) => {
    cytoscapeInstance.elements((e) => {
      e.lock();
    });
    const target = event.target;
    const parentId = target.data("parentId");
    const isShowParent = action === "show-parent";

    // fetching from query cache if exists
    const preloadedData = queryClient.getQueryData([
      GET_GRAPH_DATA,
      isShowParent ? parentId : target.id(),
    ]) as IGraphData;

    // avoiding extra API call and utilizing query cache
    let graphData = null as IGraphData;
    if (preloadedData === undefined) {
      setLoading(true);
      graphData = (await getGraphDatas(
        isShowParent ? parentId : target.id()
      )) as IGraphData;
      queryClient.setQueryData(
        [GET_GRAPH_DATA, isShowParent ? parentId : target.id()],
        graphData
      );
      setLoading(false);
    } else {
      graphData = preloadedData;
    }

    const nodesIdToInsert = [];
    if (action === "expand") {
      nodesIdToInsert.push(...identifyHyperlinkNodes(graphData, target.id()));
    } else if (action === "show-parent") {
      nodesIdToInsert.push(parentId);
    } else if (action === "show-children") {
      // For show-children, get all direct children
      const childNodes = identifyChildNodes(graphData, target.id());
      nodesIdToInsert.push(...childNodes);
    }

    const nodeToUpdate = cytoscapeInstance.getElementById(target.id());
    let nodesAlreadyPlotted = true;
    let edgesAlreadyPlotted = true;
    nodesIdToInsert?.forEach((node) => {
      if (cytoscapeInstance.getElementById(node).length === 0) {
        nodesAlreadyPlotted = false;
      }
    });
    if (nodesAlreadyPlotted) {
      // For show-children action, we need to check if nodes exist and create edges if needed
      if (action === "show-children") {
        // Always proceed with creating/showing edges for child nodes
        // We'll set this flag to true if all nodes and edges already exist and are visible
        let allNodesAndEdgesVisible = true;

        nodesIdToInsert?.forEach((node) => {
          const childNode = cytoscapeInstance.getElementById(node.toString());
          const existingEdges = cytoscapeInstance
            .edges()
            .filter(
              (edge) =>
                edge.data("source") === target.id().toString() &&
                edge.data("target") === node.toString()
            );

          // Check if node exists but is hidden
          if (childNode.length > 0 && !childNode.visible()) {
            childNode.show();
            allNodesAndEdgesVisible = false;
          }

          // Check if edge exists but is hidden
          if (existingEdges.length > 0) {
            existingEdges.forEach((edge) => {
              if (!edge.visible()) {
                edge.show();
                allNodesAndEdgesVisible = false;
              }
            });
          } else {
            // No edge exists, we'll need to create one
            allNodesAndEdgesVisible = false;
          }
        });

        // If all nodes and edges already exist and are visible, just update the state
        if (allNodesAndEdgesVisible && nodesIdToInsert.length > 0) {
          nodeToUpdate.data("isChildrensShown", true);
          return;
        }

        // Otherwise, continue with the normal flow to create missing nodes/edges
      } else {
        // For other actions, check if edges already exist
        nodesIdToInsert?.forEach((node) => {
          if (!doesEdgeExist(cytoscapeInstance, target.id(), node)) {
            edgesAlreadyPlotted = false;
          }
        });

        if (nodesAlreadyPlotted && edgesAlreadyPlotted) {
          if (action === "expand") {
            nodeToUpdate.data("hasRelations", false);
          }
          showInfoNotification(
            "Nodes already present in graph!",
            "Unable to add new nodes"
          );
          return;
        }
      }
    }
    // Update node state based on action

    if (nodeToUpdate.length > 0) {
      if (action === "expand") {
        nodeToUpdate.data("isExpanded", true);
      } else if (action === "show-parent") {
        nodeToUpdate.data("isParentShown", true);
      } else if (action === "show-children") {
        // Always set isChildrensShown to true for show-children action
        nodeToUpdate.data("isChildrensShown", true);
      }
    }

    // get current positions of nodes
    const existingPositions = {};
    cytoscapeInstance.nodes().forEach((node) => {
      const position = node.position();
      existingPositions[node.id()] = { x: position.x, y: position.y };
    });

    const newNodes = [];
    const newSelectedTemplates = [...(selectedFilters.templates || [])];
    const newTemplatesForFilter = [...filterOptions.templates];
    const updatedTemplates = [...allTemplates];

    if (!nodesAlreadyPlotted) {
      // Create group ID based on action
      const groupId =
        action === "expand"
          ? `group-links-${target.id()}`
          : `group-childs-${isShowParent ? parentId : target.id()}`;

      // Check if group already exists
      const existingGroup = cytoscapeInstance.getElementById(groupId);

      // Only create group if it doesn't already exist
      if (existingGroup.length === 0) {
        cytoscapeInstance.add({
          group: "nodes",
          data: {
            id: groupId,
            name: "group1",
            icon: getAttributeIcon("_30_folder"),
            label: "",
          },
        });
      }

      graphData.nodes.forEach((nodeInstance) => {
        if (nodesIdToInsert.includes(nodeInstance.id)) {
          const template = templatesData[Number(nodeInstance.templateId)];

          const isTemplateNotPresent =
            newTemplatesForFilter.findIndex(
              (item) => item?.id === nodeInstance.templateId
            ) === -1;

          if (isTemplateNotPresent) {
            updatedTemplates.push(nodeInstance.templateId);
            if (
              !selectedFilters?.appliedFilter.includes(nodeInstance.templateId)
            ) {
              newSelectedTemplates.push(nodeInstance.templateId);
            }

            newTemplatesForFilter.push({
              id: nodeInstance.templateId,
              title: template?.name || "-",
              key: nodeInstance.templateId,
              icon: getAttributeIcon(template?.icon || "_30_folder"),
              children: [],
            });
          } else {
            if (
              !newSelectedTemplates.includes(nodeInstance.templateId) &&
              !selectedFilters?.appliedFilter.includes(nodeInstance.templateId)
            ) {
              newSelectedTemplates.push(nodeInstance.templateId);
            }
          }

          // Check if node already exists
          const existingNode = cytoscapeInstance.getElementById(
            nodeInstance.id.toString()
          );

          if (existingNode.length > 0) {
            // Node exists, make it visible
            existingNode.show();

            // Update parent if needed for proper grouping
            if (
              action !== "expand" &&
              existingNode.data("parent") !== groupId
            ) {
              existingNode.move({ parent: groupId });
            }

            // Update node state
            if (action === "show-children") {
              existingNode.data("isParentShown", true);
            } else if (action === "show-parent") {
              // When showing a parent, we don't want to set isChildrensShown to true
              // because that would make the system think all children are shown
              existingNode.data("isParentShown", true);
            }
          } else {
            // Node doesn't exist, create it
            newNodes.push(nodeInstance.id.toString());
            let attributeId = null;
            if (action === "expand") {
              attributeId = graphData.edges.find(
                (edge) =>
                  edge.nodeTo === nodeInstance.id ||
                  edge.nodeFrom === nodeInstance.id
              )?.attributeId;
            }

            cytoscapeInstance.add({
              group: "nodes",
              data: {
                id: nodeInstance.id.toString(),
                label: nodeInstance.name,
                icon: getAttributeFile(template?.icon || "_30_folder"),
                hasChildren: nodeInstance.hasChildren,
                hasRelations: nodeInstance.hasRelations,
                hasParent: nodeInstance.hasParent,
                templateId: nodeInstance.templateId,
                template: template?.name,
                inTrash: nodeInstance?.inTrash,
                textColor: nodeInstance?.inTrash
                  ? theme.trashBreadcrumbsColor
                  : "#010102",
                parentId: nodeInstance.parentId,
                connectedNode: isShowParent ? parentId : target.id(),
                parent: groupId,
                isParentShown: action === "show-children" ? true : undefined,
                attributeId: attributeId,
                nodeType: action === "expand" ? "link" : "child",
                // Don't set isChildrensShown to true for show-parent action
                isChildrensShown: false,
              },
            });

            if (
              selectedFilters?.appliedFilter.includes(nodeInstance.templateId)
            ) {
              cytoscapeInstance
                .getElementById(nodeInstance.id.toString())
                .hide();
            }
          }
        }
      });
    }
    if (action === "show-parent") {
      // First, check if there's an existing edge (visible or hidden)
      const existingEdge = cytoscapeInstance
        .edges()
        .find(
          (edge) =>
            edge.data("source") == parentId &&
            edge.data("target") == target.id()
        );

      if (existingEdge) {
        // Edge exists, just make sure it's visible
        existingEdge.edge.show();
      } else {
        // Check if any edge with similar source/target exists
        const similarEdges = cytoscapeInstance
          .edges()
          .filter(
            (edge) =>
              edge.data("source") === parentId.toString() &&
              edge.data("target") === target.id().toString()
          );

        if (similarEdges.length > 0) {
          // Similar edge exists, make it visible
          similarEdges.forEach((edge) => edge.show());
        } else {
          // No edge exists, create a new one with a unique ID
          const sourceNode = cytoscapeInstance.getElementById(
            parentId.toString()
          );
          const targetNode = cytoscapeInstance.getElementById(
            target.id().toString()
          );

          // FIX: Ensure both nodes exist before creating an edge between them
          if (sourceNode.length > 0 && targetNode.length > 0) {
            const uniqueId = `edge-${parentId}-${target.id()}-${Date.now()}`;

            // Only add the edge if it doesn't already exist
            cytoscapeInstance.add({
              data: {
                id: uniqueId,
                attributeId: 0,
                source: parentId.toString(),
                target: target.id().toString(),
                parentId: isShowParent ? parentId : target.id(),
                label: "",
                color: "#b8b8b8",
                arrow: "triangle",
              },
            });
          }
        }
      }

      // We don't need to process any additional edges here
      // The direct parent-child edge is already created above
      // And we've modified the general edge processing logic to skip show-parent action
    } else if (action === "show-children") {
      // For show-children, we need to ensure all child nodes are connected with edges

      // Process all child nodes identified from the API data
      nodesIdToInsert.forEach((childId) => {
        // Find the edge data from the API response
        const edgeData = graphData.edges.find(
          (edge) =>
            edge.edgeType === "PARENTOF" &&
            edge.nodeFrom == target.id() &&
            edge.nodeTo == childId
        );

        if (edgeData) {
          // First, make sure the child node exists in the graph
          const childNode = cytoscapeInstance.getElementById(
            childId.toString()
          );

          // If child node doesn't exist, we can't create an edge to it
          if (childNode.length === 0) {
            return; // Skip this child node
          }

          // Make the child node visible
          childNode.show();

          // Now check if the edge already exists
          const existingEdge = cytoscapeInstance.getElementById(
            `edge-${edgeData.id}`
          );

          if (existingEdge.length > 0) {
            // Edge exists, make it visible
            existingEdge.show();
          } else {
            // Check if any edge with the same source/target exists
            const similarEdges = cytoscapeInstance
              .edges()
              .filter(
                (e) =>
                  e.data("source") === target.id().toString() &&
                  e.data("target") === childId.toString()
              );

            if (similarEdges.length > 0) {
              // Similar edge exists, make it visible
              similarEdges.forEach((e) => e.show());
            } else {
              // No edge exists, create it - but only if the target node exists
              // This is a safety check to prevent the error
              cytoscapeInstance.add({
                data: {
                  id: `edge-${edgeData.id}`,
                  attributeId: edgeData.attributeId || 0,
                  source: target.id().toString(),
                  target: childId.toString(),
                  parentId: target.id(),
                  label: "",
                  color: "#b8b8b8",
                  arrow: "triangle",
                },
              });
            }
          }
        }
      });

      // After processing all children, update the node state
      const nodeToUpdate = cytoscapeInstance.getElementById(target.id());
      if (nodeToUpdate.length > 0) {
        nodeToUpdate.data("isChildrensShown", true);
      }
    } else {
      graphData.edges.forEach((edge) => {
        const toInsertInNodeId =
          edge.nodeFrom != target.id() ? edge.nodeFrom : edge.nodeTo;
        const templateId = graphData.nodes.find(
          (node) => node.id === edge.nodeFrom
        )?.templateId;

        // For show-parent action, we need to be careful to avoid creating incorrect edges
        if (
          action ===
          ("show-parent" as "expand" | "show-children" | "show-parent")
        ) {
          // Skip this edge processing entirely for show-parent action
          // The direct parent-child edge is already handled in the show-parent specific section above
          return;
        }

        if (
          action === "expand" &&
          ["RELATION", "COMPOUND"].includes(edge.edgeType)
        ) {
          const parentTemplate = newTemplatesForFilter.find(
            (template) => template.id === templateId
          );

          if (
            !parentTemplate?.children?.some(
              (child) => child.attributeId === edge.attributeId
            )
          ) {
            newSelectedTemplates.push(edge.attributeId);
            parentTemplate?.children.push({
              id: edge.attributeId,
              attributeId: edge.attributeId,
              key: edge.attributeId,
              title: edge.relationName,
              isEdge: true,
            });
          } else {
            if (!newSelectedTemplates.includes(edge.attributeId)) {
              newSelectedTemplates.push(edge.attributeId);
            }
          }
        }

        const existingEdge = getEdgesBetween(
          cytoscapeInstance,
          target.id(),
          toInsertInNodeId
        );

        if (existingEdge.length === 0) {
          if (
            action === "expand" &&
            ["RELATION", "COMPOUND"].includes(edge.edgeType)
          ) {
            // Check if this specific edge already exists
            const existingEdge = cytoscapeInstance.getElementById(
              `edge-${edge.id}`
            );

            if (existingEdge.length > 0) {
              // Edge exists, make it visible
              existingEdge.show();
            } else if (
              !doesEdgeExist(
                cytoscapeInstance,
                target.id().toString(),
                toInsertInNodeId.toString()
              )
            ) {
              // Before creating an edge, verify that both source and target nodes exist
              const sourceNode = cytoscapeInstance.getElementById(
                edge.nodeFrom.toString()
              );
              const targetNode = cytoscapeInstance.getElementById(
                edge.nodeTo.toString()
              );

              if (sourceNode.length === 0 || targetNode.length === 0) {
                return; // Skip this edge
              }

              // Edge doesn't exist and no other edge exists between these nodes, create it
              cytoscapeInstance.add({
                data: {
                  id: `edge-${edge.id}`,
                  attributeId: edge.attributeId,
                  source: edge.nodeFrom.toString(),
                  target: edge.nodeTo.toString(),
                  label: edge?.relationName,
                  parentId: isShowParent ? parentId : target.id(),
                  color: "green",
                  templateId: templateId,
                  arrow: "none",
                },
              });
            }
          } else if (
            action ===
              ("show-children" as "expand" | "show-children" | "show-parent") &&
            edge.edgeType === "PARENTOF" &&
            edge.nodeFrom == target.id()
          ) {
            // Skip this section for show-children as we've already handled it in the dedicated section above
            // This prevents duplicate processing of the same edges
          }
        }
      });
    }

    setFilterOptions({
      ...filterOptions,
      templates: newTemplatesForFilter,
    });
    setTemplateUpdated(true);
    setSelectedFilters((prevState) => {
      return {
        ...prevState,
        templates: [...newSelectedTemplates],
      };
    });

    setAllTemplates(updatedTemplates);

    cytoscapeInstance.layout({ ...layoutOptions }).run();
    //setTimeout(() => {
    cytoscapeInstance.elements((e) => {
      e.unlock();
    });
    // }, 700);
  };

  const showParentAndConnectedNodesRecursively = (
    cytoscapeInstance,
    node,
    filterTemplates,
    selectedNodeId,
    visitedNodes = new Set()
  ) => {
    const nodeId = node.id();
    if (!visitedNodes.has(nodeId)) {
      visitedNodes.add(nodeId);

      // Show the current node
      node.show();
      if (
        filterTemplates.findIndex(
          (temp) => temp.id === node.data("templateId")
        ) === -1
      ) {
        const template = templatesData[node.data("templateId")];

        filterTemplates.push({
          id: node.data("templateId"),
          key: node.data("templateId"),
          title: template?.name,
          icon: getAttributeIcon(template?.icon || "_30_folder"),
          children: [],
        });
      }

      // Show the connected children nodes
      if (selectedNodeId != nodeId) {
        const children = node.successors();

        children.forEach((child) => {
          if (child.data("parentId") === nodeId) child.show();
        });
      }

      // If this is the node that triggered the action (the selected node)
      if (nodeId === selectedNodeId) {
        // Only show the edge between this node and its parent
        const parentId = node.data("parentId");
        if (parentId) {
          // First, check if there's an existing edge (visible or hidden)
          const existingEdges = cytoscapeInstance
            .edges()
            .filter(
              (edge) =>
                edge.data("source") == parentId && edge.data("target") == nodeId
            );

          if (existingEdges.length > 0) {
            // Edge exists, just make sure it's visible
            existingEdges.forEach((edge) => edge.show());
          } else {
            // Check if any edge with similar source/target exists
            const similarEdges = cytoscapeInstance
              .edges()
              .filter(
                (edge) =>
                  edge.data("source") === parentId.toString() &&
                  edge.data("target") === nodeId.toString()
              );

            if (similarEdges.length > 0) {
              // Similar edge exists, make it visible
              similarEdges.forEach((edge) => edge.show());
            } else {
              // No edge exists, create a new one with a unique ID
              // Use timestamp to ensure unique ID
              const uniqueId = `edge-${parentId}-${nodeId}-${Date.now()}`;

              cytoscapeInstance.add({
                data: {
                  id: uniqueId,
                  attributeId: 0,
                  source: parentId.toString(),
                  target: nodeId.toString(),
                  parentId: parentId,
                  label: "",
                  color: "#b8b8b8",
                  arrow: "triangle",
                },
              });
            }
          }
        }
      } else {
        // TODO: 'This is not the triggering node, only showing parent node'
      }

      // Show the parent node
      const parentId = node.data("parentId");
      if (parentId) {
        const parentNode = cytoscapeInstance.getElementById(parentId);

        if (parentNode.length > 0) {
          showParentAndConnectedNodesRecursively(
            cytoscapeInstance,
            parentNode,
            filterTemplates,
            selectedNodeId,
            visitedNodes
          );
        }
      }
    }
  };

  // const collapseRecursively = (cy, targetNode, node, filterTemplates) => {
  //   const successors = node.successors();
  //   const predecessors = node.predecessors();

  //   const children = successors.union(predecessors);
  //   children.forEach((child) => {
  //     if (
  //       child.isEdge() &&
  //       !!child.data("attributeId") &&
  //       child.data("source") == targetNode.id()
  //     ) {
  //       child.hide();
  //     }

  //     setTimeout(() => {
  //       if (child.isNode() && child.data("id") != targetNode.data("id")) {
  //         if (
  //           child
  //             .connectedEdges()
  //             .filter(
  //               (edge) => edge.visible() && edge.data("target") == child.id()
  //             ).length === 0
  //         ) {
  //           child.hide();

  //           const templateId = child.data("templateId");
  //           const nodeWithTemplate = doNodesWithTemplateIdExist(cy, templateId);

  //           if (nodeWithTemplate === 0) {
  //             filterTemplates = filterTemplates.filter(
  //               (filter) => templateId != filter.id
  //             );
  //           }
  //         }
  //       }

  //       if (child.isEdge()) {
  //         const index = filterTemplates.findIndex(
  //           (filter) => child.data("templateId") == filter.id
  //         );
  //         if (index !== -1) {
  //           const edgesWithAttribute = cy
  //             ?.edges()
  //             .filter(
  //               (edge) =>
  //                 edge.data("attributeId") == child.data("attributeId") &&
  //                 edge.visible()
  //             );
  //           let presentInOther = false;
  //           edgesWithAttribute?.forEach((edge) => {
  //             if (edge.data("source") != targetNode.id()) {
  //               presentInOther = true;
  //             }
  //           });
  //           if (!presentInOther) {
  //             filterTemplates[index].children = filterTemplates[
  //               index
  //             ].children.filter(
  //               (filter) => child.data("attributeId") != filter.id
  //             );
  //           }
  //         }
  //       }

  //       filterTemplates = collapseRecursively(
  //         cy,
  //         targetNode,
  //         child,
  //         filterTemplates
  //       );
  //     }, 200);
  //   });
  //   return filterTemplates;
  // };

  const recreateContextMenus = (cytoscapeInstance) => {
    if (!cytoscapeInstance) {
      return;
    }

    return cytoscapeInstance.contextMenus({
      menuItems: [
        {
          id: "expand",
          content: t("Expand"),
          selector: "node", // Show the context menu option for nodes
          onClickFunction: async function (event) {
            const selectedNode = event.target;
            // initial Load

            if (selectedNode.data("isExpanded") === undefined) {
              expandOrShowChildren(cytoscapeInstance, event, "expand");
            } else {
              const graphData = queryClient.getQueryData([
                GET_GRAPH_DATA,
                selectedNode.id(),
              ]) as IGraphData;
              const nodesIdToInsert = [
                ...identifyHyperlinkNodes(graphData, selectedNode.id()),
              ];
              const newSelectedTemplates = [
                ...(selectedFilters.templates || []),
              ];
              const newTemplatesForFilter = [...filterOptions.templates];
              const updatedTemplates = [...allTemplates];
              graphData.nodes.forEach((nodeInstance) => {
                if (nodesIdToInsert.includes(nodeInstance.id)) {
                  const template =
                    templatesData[Number(nodeInstance.templateId)];

                  const isTemplateNotPresent =
                    newTemplatesForFilter.findIndex(
                      (item) => item?.id === nodeInstance.templateId
                    ) === -1;

                  if (isTemplateNotPresent) {
                    updatedTemplates.push(nodeInstance.templateId);
                    newSelectedTemplates.push(nodeInstance.templateId);
                    newTemplatesForFilter.push({
                      id: nodeInstance.templateId,
                      title: template?.name || "-",
                      key: nodeInstance.templateId,
                      icon: getAttributeIcon(template?.icon || "_30_folder"),
                      children: [],
                    });
                  } else {
                    if (
                      !newSelectedTemplates.includes(nodeInstance.templateId)
                    ) {
                      newSelectedTemplates.push(nodeInstance.templateId);
                    }
                  }

                  cytoscapeInstance
                    .getElementById(nodeInstance.id.toString())
                    .show();
                }
              });
              graphData.edges.forEach((edge) => {
                const templateId = graphData.nodes.find(
                  (node) => node.id === edge.nodeFrom
                )?.templateId;

                if (["RELATION", "COMPOUND"].includes(edge.edgeType)) {
                  cytoscapeInstance.getElementById(`edge-${edge.id}`).show();
                  const parentTemplate = newTemplatesForFilter.find(
                    (template) => template.id === templateId
                  );

                  if (
                    !parentTemplate?.children?.some(
                      (child) => child.attributeId === edge.attributeId
                    )
                  ) {
                    newSelectedTemplates.push(edge.attributeId);
                    parentTemplate?.children.push({
                      id: edge.attributeId,
                      attributeId: edge.attributeId,
                      key: edge.attributeId,
                      title: edge.relationName,
                      isEdge: true,
                    });
                  } else {
                    if (!newSelectedTemplates.includes(edge.attributeId)) {
                      newSelectedTemplates.push(edge.attributeId);
                    }
                  }
                }
              });
              setFilterOptions({
                ...filterOptions,
                templates: newTemplatesForFilter,
              });
              setTemplateUpdated(true);
              setSelectedFilters((prevState) => {
                return {
                  ...prevState,
                  templates: [...newSelectedTemplates],
                };
              });
              setAllTemplates(updatedTemplates);
            }
          },
          disabled: false,
        },
        {
          id: "collapse",
          content: t("Collapse"),
          selector: "node", // Show the context menu option for nodes
          onClickFunction: async function (event) {
            const selectedNode = event.target;

            const updatedTemplates = [...filterOptions.templates];
            collapseNodeRecursively(
              cytoscapeInstance,
              selectedNode,
              updatedTemplates,
              selectedNode
            );
            const nodeToUpdate = cytoscapeInstance.getElementById(
              selectedNode.id()
            );
            if (nodeToUpdate.length > 0) {
              nodeToUpdate.data("isExpanded", false);
            }
            setFilterOptions({
              ...filterOptions,
              templates: [...updatedTemplates],
            });
          },
        },
        {
          id: "details",
          content: t("Details"),
          selector: "node",
          onClickFunction: function (event) {
            const target = event.target;
            setSelectedNodeForDetails({
              id: target.id(),
              label: target.data("label"),
            });
          },
        },
        {
          id: "open-in-new-tab",
          content: t("Open in new tab"),
          selector: "node", // Show the context menu option for nodes
          onClickFunction: async function (event) {
            const target = event.target;

            const hierarchyData = await getHierarchyDetails(target.id());
            window.open(
              `${window.origin}${baseUrl}/details/${
                hierarchyData.menuId
              }?nodeId=${target.id()}`
            );
          },
        },
        {
          id: "open-in-trashcan",
          content: t("View in trashcan"),
          selector: "node", // Show the context menu option for nodes
          onClickFunction: async function (event) {
            const target = event.target;
            handleTrashHyperlinkClick(Number(target.id()));
          },
        },
        {
          id: "show-parent",
          content: t("Show parent"),
          selector: "node", // Show the context menu option for nodes
          onClickFunction: function (event) {
            const target = event.target;
            if (target.data("isParentShown") === undefined) {
              expandOrShowChildren(cytoscapeInstance, event, "show-parent");
            } else {
              const parentId = target.data("parentId");
              const parentNode = cytoscapeInstance.getElementById(
                parentId.toString()
              );

              const filterTemplates = [...filterOptions.templates];
              if (parentNode.length > 0) {
                showParentAndConnectedNodesRecursively(
                  cytoscapeInstance,
                  target,
                  filterTemplates,
                  target.id()
                );

                const nodeToUpdate = cytoscapeInstance.getElementById(
                  target.id()
                );
                if (nodeToUpdate.length > 0) {
                  nodeToUpdate.data("isParentShown", true);
                }
                // cytoscapeInstance
                //   .layout({ ...layoutOptions, animate: "end" })
                //   .run();
                setFilterOptions({
                  ...filterOptions,
                  templates: filterTemplates,
                });
              }
            }
          },
        },
        {
          id: "hide-parent",
          content: t("Hide parent"),
          selector: "node", // Show the context menu option for nodes
          onClickFunction: function (event) {
            const target = event.target;
            const parentId = target.data("parentId");
            const parentNode = cytoscapeInstance.getElementById(
              parentId.toString()
            );
            const filterTemplates = [...filterOptions.templates];

            if (parentNode.length > 0) {
              // Find the edge between this node and its parent
              const edgesToRemove = cytoscapeInstance
                .edges()
                .filter(
                  (edge) =>
                    edge.data("source") == parentId &&
                    edge.data("target") == target.id() &&
                    edge.visible()
                );

              // Remove the edge
              if (edgesToRemove.length > 0) {
                edgesToRemove.hide();

                // Check if parent has other visible connections
                const otherConnections = parentNode
                  .connectedEdges()
                  .filter((edge) => edge.visible());

                // Only hide parent if it has no other visible connections
                if (otherConnections.length === 0) {
                  parentNode.hide();

                  // Update template filters if needed
                  const parentTemplateId = parentNode.data("templateId");
                  const nodeWithTemplate = doNodesWithTemplateIdExist(
                    cytoscapeInstance,
                    parentTemplateId
                  );

                  if (
                    nodeWithTemplate === 0 &&
                    parentTemplateId != target.data("templateId")
                  ) {
                    const index = filterTemplates.findIndex(
                      (item) => item.id == parentTemplateId
                    );
                    if (index !== -1) {
                      filterTemplates.splice(index, 1);
                    }
                  }
                }
              }

              // Update the node state
              const nodeToUpdate = cytoscapeInstance.getElementById(
                target.id()
              );
              if (nodeToUpdate.length > 0) {
                nodeToUpdate.data("isParentShown", false);
              }

              setFilterOptions({
                ...filterOptions,
                templates: filterTemplates,
              });
            }
          },
        },
        {
          id: "show-children",
          content: t("Show children"),
          selector: "node",
          onClickFunction: async function (event) {
            // Call expandOrShowChildren to load all children and connect them
            // This will use cached data if available
            await expandOrShowChildren(
              cytoscapeInstance,
              event,
              "show-children"
            );
          },
        },
        {
          id: "hide-children",
          content: t("Hide children"),
          selector: "node", // Show the context menu option for nodes
          onClickFunction: function (event) {
            const selectedNode = event.target;

            const updatedTemplates = [...filterOptions.templates];
            hideChildrenRecursively(
              cytoscapeInstance,
              selectedNode,
              updatedTemplates,
              selectedNode
            );

            // Update the node state to indicate children are hidden
            const nodeToUpdate = cytoscapeInstance.getElementById(
              selectedNode.id()
            );
            if (nodeToUpdate.length > 0) {
              nodeToUpdate.data("isChildrensShown", false);
            }

            setFilterOptions({
              ...filterOptions,
              templates: [...updatedTemplates],
            });
          },
        },
        {
          id: "show-hidden-childrens",
          content: t("Show hidden children"),
          selector: "node",
          onClickFunction: async function (event) {
            const selectedNode = event.target;
            const filterTemplates = [...filterOptions.templates];

            const selectedNodeId = selectedNode.id();

            const selfNode = cytoscapeInstance.getElementById(
              selectedNodeId.toString()
            );
            if (selfNode.length > 0) {
              const children = getSuccessorNodes(selectedNode);
              // Hide each child
              children.forEach((child) => {
                if (
                  child.data("parentId") === selfNode.id() &&
                  child.data("nodeType") === "link"
                ) {
                  return;
                }
                child.show();
                if (child.data("isExpanded")) {
                  child.data("isExpanded", false);
                }

                if (child.data("templateId")) {
                  if (
                    !filterTemplates.find(
                      (template) => template.id === child.data("templateId")
                    )
                  ) {
                    const template = templatesData[child.data("templateId")];

                    filterTemplates.push({
                      id: child.data("templateId"),
                      key: child.data("templateId"),
                      title: template?.name,
                      icon: getAttributeIcon(template?.icon || "_30_folder"),
                      children: [],
                    });
                  }
                }
                if (
                  child.data("nodeType") === "link" &&
                  child.data("templateId")
                ) {
                  const edge = cytoscapeInstance.edges(
                    `[source = "${child.data(
                      "parentId"
                    )}"][target = "${child.id()}"]`
                  );
                  const parentTemplateFilter = filterTemplates.find(
                    (template) => template.id === child.data("templateId")
                  );

                  if (
                    parentTemplateFilter.children.findIndex(
                      (item) => item.id === edge[0].data("attributeId")
                    ) === -1
                  ) {
                    parentTemplateFilter.children.push({
                      id: edge[0].data("attributeId"),
                      attributeId: edge[0].data("attributeId"),
                      key: edge[0].data("attributeId"),
                      title: edge[0].data("label"),
                      isEdge: true,
                    });
                  }
                }
              });
              selfNode.data("isChildrensShown", true);
              setFilterOptions({
                ...filterOptions,
                templates: filterTemplates,
              });
              // cytoscapeInstance
              //   .layout({ ...layoutOptions, animate: "end" })
              //   .run();
            }
          },
        },
        {
          id: "hide-node",
          content: t("Hide node"),
          selector: "node", // Show the context menu option for nodes
          onClickFunction: async function () {
            const selectedNodes = cytoscapeInstance.elements(":selected");
            let updatedTemplates = [...filterOptions.templates];
            let selectedTemplateId = null;
            let parentOfSelectedNodeId = null;
            let isParentNodePresent = false;

            selectedNodes.forEach((selectedNode) => {
              const parent = cytoscapeInstance.getElementById(
                selectedNode.data("parentId")
              );
              if (!parent || !parent.visible()) {
                isParentNodePresent = true;
              }
            });

            if (isParentNodePresent) {
              showErrorNotification("Cannot hide root node!");
              return;
            }

            const hiddenNodes = new Set();

            const hideNode = (node) => {
              if (!hiddenNodes.has(node.id())) {
                hiddenNodes.add(node.id());
                node.connectedEdges().forEach((edge) => {
                  edge.hide();

                  const targetNode = edge.target();

                  if (targetNode) {
                    targetNode.hide();
                    const nodesWithTemplate = doNodesWithTemplateIdExist(
                      cytoscapeInstance,
                      targetNode.data("templateId")
                    );
                    if (nodesWithTemplate === 0) {
                      updatedTemplates = updatedTemplates.filter(
                        (item) => item.id !== targetNode.data("templateId")
                      );
                    }

                    hideNode(targetNode);
                  }
                });
              }
            };

            selectedNodes.forEach((selectedNode) => {
              hideNode(selectedNode);
            });

            selectedNodes.forEach((selectedNode) => {
              setTimeout(() => {
                selectedNode.hide();

                const parentOfSelectedNode = cytoscapeInstance.getElementById(
                  selectedNode.data("parentId")
                );
                cytoscapeInstance.nodes().unselect();
                parentOfSelectedNode.select();
                const templateId = parentOfSelectedNode.data().templateId;
                const nodesWithTemplate = doNodesWithTemplateIdExist(
                  cytoscapeInstance,
                  selectedNode.data("templateId")
                );
                if (nodesWithTemplate === 0) {
                  updatedTemplates = updatedTemplates.filter(
                    (item) => item.id !== selectedNode.data("templateId")
                  );
                }
                selectedTemplateId = templateId;
                parentOfSelectedNodeId = parentOfSelectedNode.id();
              }, 200);
            });

            setSelectedTemplateId(selectedTemplateId);
            setSelectedNodeId(parentOfSelectedNodeId);
            setFilterOptions({
              ...filterOptions,
              templates: [...updatedTemplates],
            });
          },
        },
      ],
      menuItemClasses: "cxtmenu-item",
      contextMenuClasses: "cxtmenu",
    });
  };

  useEffect(() => {
    if (cy) {
      recreateContextMenus(cy);
    }
  }, [i18next.language]);

  useEffect(() => {
    if (!cy) {
      return;
    }
    if (isTemplateUpdated) {
      recreateContextMenus(cy);
    }
  }, [
    filterOptions.templates,
    selectedFilters.templates,
    selectedFilters.appliedFilter,
  ]);

  const getSuccessorNodes = (selectedNode) => {
    const successorElements = selectedNode.successors();

    return successorElements.filter(
      (el) =>
        (el.isNode() &&
          selectedNode
            .outgoers()
            .filter((edge) => edge.target().id() === el.id()).length > 0) ||
        el.isEdge()
    );
  };

  const handleMouseOut = () => {
    setTooltip({ display: false, x: 0, y: 0, text: "" });
  };

  useEffect(() => {
    if (!extensionsRegistered.current) {
      cytoscape.use(cose);
      cytoscape.use(contextMenus);
      extensionsRegistered.current = true;
    }
    setSelectedNodeId(nodeId);
    const cyInstance = cytoscape({
      container: document.getElementById(
        fromTrashcan ? "trash-cy" : "content-cy"
      ),
      elements,
      style: graphStyles as any,
      layout: layoutOptions,
      motionBlur: true,
      wheelSensitivity: 1.5,
    }) as cytoscape.Core;

    // Restore viewport if saved
    const savedViewport = localStorage.getItem(VIEWPORT_KEY);
    if (savedViewport) {
      try {
        const { zoom, pan } = JSON.parse(savedViewport);
        if (zoom) cyInstance.zoom(zoom);
        if (pan) cyInstance.pan(pan);
      } catch (e) {
        // Ignore errors
      }
    }

    // Ensure all nodes have isChildrensShown property initialized
    cyInstance.nodes().forEach((node) => {
      if (node.data("isChildrensShown") === undefined) {
        node.data("isChildrensShown", false);
      }
    });

    cyInstance.panningEnabled(true);
    cyInstance.center();

    // Enable context menu for nodes
    const instance = recreateContextMenus(cyInstance);

    cyInstance.on("click", "node", (event) => {
      const clickedNode = event.target;

      // Deselect the previously selected node
      if (selectedNodeId !== null) {
        const previousSelectedNode = cyInstance.$("#" + selectedNodeId);
        previousSelectedNode.unselect();
      }

      // Select the clicked node
      clickedNode.select();

      // Update the selectedNodeId
      setSelectedNodeId(clickedNode.id());
    });

    cyInstance.on("mouseover", "node", function (event) {
      const node = event.target;
      if (!node.id().startsWith("group")) {
        const pos = node.renderedPosition();
        setTimeout(() => {
          setTooltip({
            display: true,
            x: pos.x,
            y: pos.y,
            text: node.data("template") || node.data("label"),
          });
        }, 600);
      }
    });
    cyInstance.on("mouseout", "node", handleMouseOut);

    cyInstance.on("tap", "node", function (evt) {
      const node = evt.target;
      const templateId = node.data().templateId;
      setSelectedTemplateId(templateId);
    });

    cyInstance.on("cxttap", "node", function (event) {
      // hiding
      instance.hideMenuItem("hide-children");
      instance.hideMenuItem("show-children");
      instance.hideMenuItem("show-parent");
      instance.hideMenuItem("expand");
      instance.hideMenuItem("collapse");
      instance.hideMenuItem("hide-parent");
      instance.hideMenuItem("show-hidden-childrens");
      // instance.showMenuItem("hide-node");
      instance.hideMenuItem("hide-node");
      instance.showMenuItem("details");
      instance.hideMenuItem("open-in-new-tab");
      instance.hideMenuItem("open-in-trashcan");
      // select node and template on click

      const selectedNode = event.target;

      if (selectedNode.id().startsWith("group")) {
        instance.hideMenuItem("details");
        return;
      }

      if (selectedNode.data("inTrash")) {
        instance.showMenuItem("open-in-trashcan");
      } else {
        instance.showMenuItem("open-in-new-tab");
      }

      const selectedNodes = cyInstance.elements(":selected");
      if (selectedNodes.length === 1) {
        cyInstance.nodes().unselect();
      }
      selectedNode.select();

      const nodeData = selectedNode.data() as IGraphNodes;
      setSelectedNodeId(selectedNode.id());
      setSelectedTemplateId(nodeData.templateId);

      const hasVisibleHyperlinks = selectedNode
        .connectedEdges()
        .some(
          (edge) =>
            edge.visible() &&
            edge.data("parentId") == selectedNode.id() &&
            !!edge.data("attributeId")
        );

      // const hasVisibleChildrens = selectedNode
      //   .connectedEdges()
      //   .some(
      //     (edge) =>
      //       edge.visible() &&
      //       edge.data("parentId") == selectedNode.id() &&
      //       !edge.data("attributeId")
      //   );

      // Check if there's an edge between this node and its parent
      const edgeToParentExists = doesEdgeToParentExist(
        cyInstance,
        selectedNode.id(),
        selectedNode.data().parentId
      );

      if (nodeData.hasRelations) {
        if (hasVisibleHyperlinks) {
          instance.showMenuItem("collapse");
        } else {
          instance.showMenuItem("expand");
        }
      }

      if (nodeData.hasChildren) {
        // We need to determine if this node was explicitly expanded with "Show children"
        // or if it just has some connections due to "Show parent" from its children

        // First check if the node has the isChildrensShown flag explicitly set to true
        const childrenExplicitlyShown =
          selectedNode.data("isChildrensShown") === true;

        // Then check if there are any visible child edges
        // const visibleChildEdges = selectedNode
        //   .connectedEdges()
        //   .filter(
        //     (edge) =>
        //       edge.visible() &&
        //       edge.data("source") === selectedNode.id() &&
        //       !edge.data("attributeId")
        //   );

        // Only show "Hide children" if children were explicitly shown with "Show children"
        // or if all possible children are visible (which means they were all shown)
        if (childrenExplicitlyShown) {
          instance.showMenuItem("hide-children");
        } else {
          // Always show "Show children" if not explicitly shown before
          instance.showMenuItem("show-children");
        }
      }

      if (nodeData.hasParent) {
        // Show "hide-parent" if there's an edge between this node and its parent
        // Show "show-parent" if parent exists but there's no edge, or if parent doesn't exist
        if (edgeToParentExists) {
          instance.showMenuItem("hide-parent");
          instance.hideMenuItem("show-parent");
        } else {
          instance.hideMenuItem("hide-parent");
          instance.showMenuItem("show-parent");
        }
      }
      // }
    });

    if (nodeId) {
      const nodeIdToSelect = nodeId.toString();
      const selectedNode = cyInstance.getElementById(nodeIdToSelect);

      if (selectedNode.length > 0) {
        selectedNode.select();
      }
    }

    setCy(cyInstance);

    return () => {
      cyInstance.destroy();
    };
  }, [fromTrashcan]);

  useEffect(() => {
    if (!cy) return;

    const saveViewport = () => {
      const viewport = {
        zoom: cy.zoom(),
        pan: cy.pan(),
      };
      localStorage.setItem(VIEWPORT_KEY, JSON.stringify(viewport));
    };

    cy.on("zoom pan", saveViewport);

    // Clean up
    return () => {
      cy.off("zoom pan", saveViewport);
    };
  }, [cy]);

  // Event handler for deselecting nodes on outside click
  const handleClickOutside = (event) => {
    const clickedElement = event.target;

    // Check if the click is outside the Cytoscape container
    if (!clickedElement.closest("#cy-container")) {
      // Deselect the selected node
      if (selectedNodeId !== null) {
        const selectedNode = cy.$("#" + selectedNodeId);
        selectedNode.select();
        // setSelectedNodeId(null);
      }
    }
  };

  useEffect(() => {
    if (!cy) {
      return;
    }

    document.addEventListener("click", handleClickOutside);

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [cy, selectedNodeId]);

  useEffect(() => {
    if (!cy) {
      return;
    }
    cy.style()
      .selector("node") // Replace with a class or selector that matches the nodes you want to hide labels for
      .style({
        label: displayOptions.includes("object-names") ? "data(label)" : "",
      })
      .update();
    // cy.zoom(0.5); // Adjust the initial zoom level as needed
    // cy.panningEnabled(true);

    cy.style()
      .selector("edge")
      .style({
        label: displayOptions.includes("relations-name") ? "data(label)" : "",
      })
      .update();

    // try {
    //   cy.layout({ ...layoutOptions, animate: "start" }).run();
    // } catch (e) {
    //   //
    // }
  }, [cy, displayOptions]);

  const filterGraph = () => {
    if (!cy) {
      return;
    }

    cy.edges().forEach((edge) => {
      edge.style("line-color", ""); // Reset line color
      edge.style("width", ""); // Reset line width
    });

    const selectedNode = cy.getElementById(selectedNodeId);
    const connectedEdges = selectedNode.connectedEdges();
    connectedEdges.forEach((edge) => {
      edge.style("width", 3); // Change line width for better visibility
    });

    // Refresh the layout and style
    if (
      cy &&
      typeof cy === "object" &&
      cy?.layout &&
      typeof cy.layout === "function"
    ) {
      setTimeout(() => {
        hideEmptyGroups();
      }, 200);

      const originalPositions = {};
      cy.nodes().forEach(function (node) {
        if (!node.isParent()) {
          // node.on("mouseover", (event) => {
          //   const tooltipId = `tooltip-${node.id()}`;
          //   const tooltipElement = document.getElementById(tooltipId);

          //   if (!tooltipElement) {
          //     const div = document.createElement("div");
          //     div.id = tooltipId;
          //     div.className = "tooltip";
          //     div.innerHTML = `${node.data("template")}`;
          //     div.style.position = "absolute";
          //     div.style.fontSize = "12px";
          //     div.style.top = `${event.originalEvent.clientY}px`;
          //     div.style.left = `${event.originalEvent.clientX}px`;
          //     div.style.background = "#f4f0f0";
          //     div.style.pointerEvents = "none";
          //     div.style.padding = "5px";
          //     div.style.borderRadius = "3px";
          //     div.style.zIndex = "1";

          //     document.body.appendChild(div);
          //   }
          // });

          node.on("mouseout", () => {
            const tooltipId = `tooltip-${node.id()}`;
            const tooltipElement = document.getElementById(tooltipId);

            if (tooltipElement) {
              document.body.removeChild(tooltipElement);
            }
          });
        }
        originalPositions[node.id()] = {
          x: node.position("x"),
          y: node.position("y"),
        };
      });

      // try {
      //   cy.layout({ ...layoutOptions, animate: "end" }).run();
      // } catch (e) {
      //   //
      // }

      // Restore the original positions of nodes
      cy.nodes().forEach(function (node) {
        const originalPosition = originalPositions[node.id()];
        if (originalPosition) {
          node.position("x", originalPosition.x);
          node.position("y", originalPosition.y);
        }
      });

      // cy.style().update();
    }
  };

  useEffect(() => {
    if (!cy) {
      return;
    }
    cy.edges().forEach((edge) => {
      edge.style("line-color", ""); // Reset line color
      edge.style("width", ""); // Reset line width
    });

    const selectedNode = cy.getElementById(selectedNodeId);
    const connectedEdges = selectedNode.connectedEdges();
    connectedEdges.forEach((edge) => {
      edge.style("width", 3); // Change line width for better visibility
    });
  }, [cy, selectedNodeId]);

  useEffect(() => {
    if (cy) filterGraph();
  }, [cy, selectedFilters.templates, filterOptions.templates]);

  // Orphan filter logic using detectOrphanNodes criteria and tracking
  useEffect(() => {
    if (!cy) return;
    const orphansChecked = selectedFilters.display?.includes("orphans");
    toggleOrphanFilter(cy, orphansChecked);
    hideEmptyGroups();
  }, [cy, selectedFilters.display, selectedNodeId]);

  function hideConnectedNodesRecursively(node, allTemplates) {
    // Hide the current node
    if (node.id() !== selectedNodeId) {
      node.hide();
      const targetNodes = cy.nodes(
        "[templateId=" + node.data("templateId") + "]:visible"
      );
      if (
        targetNodes.length === 0 &&
        allTemplates?.includes(node.data("templateId"))
      ) {
        const indexToRemove = allTemplates.indexOf(node.data("templateId"));

        if (indexToRemove !== -1) {
          // Use slice to remove the element from the array
          allTemplates.splice(indexToRemove, 1);
        }
      }
      // Traverse through the connected edges
      node.connectedEdges().forEach(function (edge) {
        const connectedNode = edge.target();
        if (connectedNode.visible()) {
          hideConnectedNodesRecursively(connectedNode, allTemplates);
        }
      });
    }
  }

  const hideEmptyGroups = () => {
    if (!cy) {
      return;
    }
    cy.nodes().forEach(function (node) {
      if (node.isParent()) {
        node.show();
        // Check if the node is a group (parent)
        const visibleChildren = node.children(":visible");

        if (visibleChildren.length === 0) {
          node.hide();
        }
      }
    });
  };

  const handleFilterSelection = (hiddenTemplates) => {
    if (hiddenTemplates.length > 0) {
      cy.nodes().forEach((node) => {
        const templateId = node.data("templateId");
        if (hiddenTemplates.includes(templateId)) {
          node.hide();
        } else {
          node.show();
        }
      });
    } else {
      cy.nodes().show();
    }
  };

  const handleTemplateClick = (node) => {
    let newtemp = [...(selectedFilters?.templates || [])];
    if (node.isEdge) {
      if (node.checked) {
        newtemp = newtemp?.filter((item) => item !== node.id);

        const targetEdges = cy.edges().filter(function (edge) {
          return (
            edge.data("attributeId") === node.attributeId && edge.visible()
          );
        });

        targetEdges?.forEach((edge) => {
          const targetNode = edge.target();
          edge.hide();
          if (targetNode.id() !== selectedNodeId) {
            const connectedEdges = targetNode.connectedEdges(":visible");
            const allSameAttribute = connectedEdges.every(
              (e) => e.data("attributeId") === edge.data("attributeId")
            );
            if (allSameAttribute) targetNode.hide();

            const nodes = cy.nodes(
              "[templateId=" + targetNode.data("templateId") + "]"
            );
            if (
              newtemp.includes(targetNode.data("templateId")) &&
              nodes.length === targetEdges.length
            ) {
              newtemp = newtemp?.filter(
                (item) => item !== targetNode.data("templateId")
              );
            }
          }
        });
      } else {
        newtemp.push(node.id);
        const targetEdges = cy.edges().filter(function (edge) {
          return edge.data("attributeId") === node.attributeId;
        });

        targetEdges?.forEach((edge) => {
          const targetNode = edge.target();
          targetNode.show();
          edge.show();

          if (!newtemp.includes(targetNode.data("templateId"))) {
            newtemp.push(targetNode.data("templateId"));
          }
        });
      }
    } else {
      // if template unchecked
      if (node.checked) {
        newtemp = newtemp?.filter((item) => item !== node.id);
        const index = filterOptions.templates.findIndex(
          (temp) => temp.id === node.id
        );
        if (index !== -1) {
          if (filterOptions.templates[index].children.length > 0) {
            filterOptions.templates[index].children?.forEach((child) => {
              newtemp = newtemp?.filter((item) => item !== child.id);
            });
          }
        }
        const targetNodes = cy.nodes("[templateId=" + node.id + "]");

        targetNodes?.forEach((targets) => {
          hideConnectedNodesRecursively(targets, newtemp);
          const edgeWithAttributeId = cy
            .edges()
            .filter(
              (item) =>
                !!item.data("attributeId") &&
                item.data("attributeId") === targets.data("attributeId")
            );
          if (
            !!targets.data("attributeId") &&
            newtemp.includes(targets.data("attributeId")) &&
            edgeWithAttributeId.length === targetNodes.length
          ) {
            newtemp = newtemp?.filter(
              (item) => item !== targets.data("attributeId")
            );
          }
        });
      } else {
        // if template checked
        newtemp.push(node.id);
        const targetNodes = cy.nodes("[templateId=" + node.id + "]");

        targetNodes?.forEach((targets) => {
          targets.show();
          const connectedEdges = targets.connectedEdges();
          connectedEdges.forEach((edge) => edge.show());
          // const children = targets.successors();
          // children.forEach((child) => {
          //   child.show();
          // });

          if (
            !!targets.data("attributeId") &&
            !newtemp.includes(targets.data("attributeId"))
          ) {
            newtemp.push(targets.data("attributeId"));
          }
        });
      }
    }
    setTemplateUpdated(true);

    setSelectedFilters({ ...selectedFilters, templates: [...newtemp] });
  };

  const STATE_KEY = "graph-filters";
  const [sidebarWidth, setSidebarWidth] = useState(260);

  useEffect(() => {
    const width = localStorage.getItem(STATE_KEY);
    if (width) setSidebarWidth(Number(width));
  }, []);

  return (
    <>
      <div style={{ position: "relative", width: "100%" }}>
        {loading && (
          <Loader>
            <LoadingOutlined />
          </Loader>
        )}
        <div
          id={fromTrashcan ? "trash-cy" : "content-cy"}
          style={{ width: "100%", height: "100vh" }}
        />
        {tooltip.display && (
          <Tooltip x={tooltip.x} y={tooltip.y} text={tooltip.text} />
        )}
      </div>
      <ResizableDiv
        resize="left"
        onResize={(_event, _direction, ref) => {
          if (ref.offsetWidth > 80) {
            setSidebarWidth(ref.offsetWidth);
          }
        }}
        maxWidth={"100%"}
        minWidth={80}
        width={sidebarWidth}
        defaultWidth="260px"
        saveWidthToLocalStorage
        stateKey={STATE_KEY}
      >
        <FilterWrapper>
          <GraphFilterTree
            selectedFilters={selectedFilters}
            allTemplates={allTemplates}
            setSelectedFilters={setSelectedFilters}
            filterOptions={filterOptions}
            setFilterOptions={setFilterOptions}
            onFilter={handleFilterSelection}
          />
          <DisplayFilters
            selectedFilters={selectedFilters}
            setSelectedFilters={setSelectedFilters}
          />
          <TemplatesFilter
            treeData={filterOptions.templates}
            selectedFilters={selectedFilters}
            handleTemplateClick={handleTemplateClick}
            selectedTemplateId={selectedTemplateId}
          />
        </FilterWrapper>
      </ResizableDiv>
      {contextHolder}
      {!!selectedNodeForDetails && (
        <DetailsContainer
          title={selectedNodeForDetails.label}
          isOpen={!!selectedNodeForDetails}
          id={selectedNodeForDetails.id}
          onClose={() => setSelectedNodeForDetails(null)}
        />
      )}
    </>
  );
};

export { CytoscapeWithContextMenu };

const Loader = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 50px;
  background: #09090908;
  color: #4277a2;
  position: absolute;
  inset: 0;
  z-index: 1;
`;

const FilterWrapper = styled.div`
  border-left: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
  margin-right: 20px;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
`;

const baseUrl =
  import.meta.env.VITE_APP_BASE_URL === "/"
    ? ""
    : import.meta.env.VITE_APP_BASE_URL;
