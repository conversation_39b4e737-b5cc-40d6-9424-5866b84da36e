import { IGraphData } from "../../../interfaces";

export const doesEdgeExist = (
  cy,
  sourceNodeId: string,
  targetNodeId: string
) => {
  const edges = cy.edges(
    `[source='${sourceNodeId}'][target='${targetNodeId}'], [source='${targetNodeId}'][target='${sourceNodeId}']`
  );
  return edges.length > 0;
};

export const getEdgesBetween = (cytoscapeInstance, sourceId, targetId) => {
  return cytoscapeInstance.edges(
    '[source="' +
      sourceId.toString() +
      '"][target="' +
      targetId.toString() +
      '"]'
  );
};

export const identifyChildNodes = (data: IGraphData, selectedNodeId) => {
  const childNodes = [];
  data.edges?.forEach((edge) => {
    if (edge.edgeType === "PARENTOF" && edge.nodeFrom == selectedNodeId) {
      childNodes.push(edge.nodeTo);
    }
  });
  return childNodes;
};

export const identifyParentNodes = (data: IGraphData) => {
  const hyperlinkNodes = [];
  data.edges?.forEach((edge) => {
    if (edge.edgeType === "CHILDOF") {
      hyperlinkNodes.push(edge.nodeFrom);
    }
  });
  return hyperlinkNodes;
};

export const identifyHyperlinkNodes = (data: IGraphData, parentId) => {
  const hyperlinkNodes = [];
  data.edges?.forEach((edge) => {
    if (["RELATION", "COMPOUND"].includes(edge.edgeType)) {
      if (edge.nodeTo != parentId) {
        hyperlinkNodes.push(edge.nodeTo);
      }

      if (edge.nodeFrom != parentId) {
        hyperlinkNodes.push(edge.nodeFrom);
      }
    }
  });
  return hyperlinkNodes;
};

export const doNodesWithTemplateIdExist = (cy, templateId) => {
  const nodesWithTemplateId = cy?.nodes().filter(function (node) {
    return node.data("templateId") == templateId && node.visible();
  });
  return nodesWithTemplateId?.length || null;
};

export const doNodesWithParentIdExist = (cy, parentId) => {
  return cy?.nodes().some(function (node) {
    return node.data("id") == parentId && node.visible();
  });
};

// Check if an edge exists between a node and its parent
export const doesEdgeToParentExist = (cy, nodeId, parentId) => {
  const edges = cy
    .edges()
    .some(
      (edge) =>
        edge.data("source") == parentId &&
        edge.data("target") == nodeId &&
        edge.visible()
    );
  return edges;
};

export const hideInternalNodes = (node, originalParent, child) => {
  node
    .neighborhood()
    .nodes()
    .forEach((_node) => {
      if (_node.id() == originalParent.id() || child.id() == _node.id()) {
        return;
      }

      _node.hide();

      if (_node.neighborhood().nodes().length > 1) {
        hideInternalNodes(_node, originalParent, node);
      }
    });
};

/**
 * Toggles the visibility of orphan nodes based on the 'orphansChecked' flag.
 *
 * @param cy Cytoscape instance
 * @param orphansChecked Boolean flag indicating whether the orphan filter is enabled
 */
export function toggleOrphanFilter(cy, orphansChecked) {
  cy.nodes().forEach((node) => {
    if (!node.isParent() && !node.id().startsWith("group")) {
      const visibleEdges = node
        .connectedEdges()
        .filter((edge) => edge.visible());
      const isOrphan = visibleEdges.length === 0;
      const isActive = node.selected && node.selected();

      if (isOrphan) {
        if (orphansChecked) {
          // Only show if it was hidden by the orphan filter
          if (node.data("hiddenByOrphanFilter")) {
            node.show();
            node.data("hiddenByOrphanFilter", false);
          }
        } else {
          if (!isActive && node.visible()) {
            node.hide();
            node.data("hiddenByOrphanFilter", true);
          }
        }
      } else {
        // Clear the flag if node is not orphan anymore
        if (node.data("hiddenByOrphanFilter")) {
          node.data("hiddenByOrphanFilter", false);
        }
      }
    }
  });
}
