import { doNodesWithTemplateIdExist } from "./functions";

//  blue color => pointing nodes
// green color => referenced nodes

export const hideParentRecursively = (
  cy,
  node,
  filterTemplates,
  originalNode,
  visitedNodes = new Set()
) => {
  const parentId = node.data("parentId");
  if (parentId && !visitedNodes.has(node.id())) {
    visitedNodes.add(node.id());
    const parentNode = cy.getElementById(parentId);
    if (parentNode.id() != originalNode.id()) {
      // Reset the expansion state of the parent node if it was expanded
      if (parentNode.data("isExpanded") === true) {
        parentNode.data("isExpanded", false);
      }

      // Reset the parent shown state if it was showing its parent
      if (parentNode.data("isParentShown") === true) {
        parentNode.data("isParentShown", false);
      }

      parentNode.hide();

      const parentTemplateId = parentNode.data("templateId");
      const nodeWithTemplate = doNodesWithTemplateIdExist(cy, parentTemplateId);

      if (
        nodeWithTemplate === 0 &&
        parentTemplateId != originalNode.data("templateId")
      ) {
        const index = filterTemplates.findIndex(
          (item) => item.id == parentTemplateId
        );
        filterTemplates.splice(index, 1);
      }

      const children = parentNode.successors();
      children.forEach((child) => {
        if (
          child.data("parentId") == originalNode.id() ||
          child.id().startsWith("edge")
        ) {
          return;
        }
        if (child.id() != originalNode.id() && child) {
          // Reset the expansion state of this child node if it was expanded
          if (child.data("isExpanded") === true) {
            child.data("isExpanded", false);
          }

          // Reset the children shown state if it was showing children
          if (child.data("isChildrensShown") === true) {
            child.data("isChildrensShown", false);
          }

          child.hide();

          const templateId = child.data("templateId");
          if (
            templateId != originalNode.data("templateId") &&
            !child.id().startsWith("edge")
          ) {
            const nodeWithTemplate = doNodesWithTemplateIdExist(cy, templateId);
            if (nodeWithTemplate === 0) {
              const index = filterTemplates.findIndex(
                (item) => item.id == templateId
              );
              filterTemplates.splice(index, 1);
            }
          }
        }
      });

      if (parentNode.data("parentId") && parentNode.data("isParentShown")) {
        if (parentNode.id() != originalNode.id())
          hideParentRecursively(
            cy,
            parentNode,
            filterTemplates,
            originalNode,
            visitedNodes
          );
      }
    }
  }
};

export const hideChildrenRecursively = (
  cy,
  node,
  filterTemplates,
  originalParent
) => {
  // Get direct child edges (edges where this node is the source)
  const childEdges = node
    .outgoers()
    .filter(
      (ele) =>
        ele.isEdge() &&
        !ele.data("attributeId") &&
        ele.data("source") === node.id()
    );

  // Process each child edge
  childEdges.forEach((edge) => {
    const childNode = edge.target();

    // Skip if this is not a valid child
    if (
      childNode.id() === originalParent.id() ||
      childNode.id() === originalParent.data("parentId")
    ) {
      return;
    }

    // Reset the expansion state of this child node if it was expanded
    if (childNode.data("isExpanded") === true) {
      childNode.data("isExpanded", false);
    }

    // Also reset the children shown state if it was showing children
    if (childNode.data("isChildrensShown") === true) {
      childNode.data("isChildrensShown", false);
    }

    // Hide the edge connecting parent to child
    edge.hide();

    // Check if child has other visible connections
    const otherVisibleConnections = childNode
      .connectedEdges()
      .filter((e) => e.id() !== edge.id() && e.visible());

    // If no other visible connections, hide the child node
    if (otherVisibleConnections.length === 0) {
      childNode.hide();

      // Update template filters if needed
      if (childNode.data("templateId") !== originalParent.data("templateId")) {
        const templateId = childNode.data("templateId");
        const nodesWithTemplate = cy
          .nodes()
          .filter((n) => n.visible() && n.data("templateId") === templateId);

        if (nodesWithTemplate.length === 0) {
          const index = filterTemplates.findIndex(
            (item) => item.id === templateId
          );
          if (index !== -1) {
            filterTemplates.splice(index, 1);
          }
        }
      }

      // Recursively process this child's children
      hideChildrenRecursively(cy, childNode, filterTemplates, originalParent);
    }
  });

  // Check if the current node is not the original parent, then hide it
  if (node !== originalParent) {
    const visibleEdges = node.connectedEdges().filter((e) => e.visible());
    if (visibleEdges.length === 0) {
      node.hide();
    }
  }
};

export const collapseNodeRecursively = (
  cy,
  node,
  filterTemplates,
  originalParent,
  visitedNodes = new Set(), // Track visited nodes
  processedEdges = new Set() // Track processed edges to avoid duplicates
) => {
  // Check if the node is already visited to avoid infinite recursion
  if (visitedNodes.has(node.id())) {
    return;
  }

  // Mark the current node as visited
  visitedNodes.add(node.id());

  // Get all connected edges that represent expansion relationships
  const expansionEdges = node.connectedEdges().filter((edge) => {
    // Skip already processed edges
    if (processedEdges.has(edge.id())) {
      return false;
    }

    // Mark this edge as processed
    processedEdges.add(edge.id());

    // Only consider visible edges
    if (!edge.visible()) {
      return false;
    }

    // Skip parent-child relationship edges (these have attributeId = 0)
    if (edge.data("attributeId") === 0) {
      return false;
    }

    // Include edges that are part of expansion (have attributeId)
    return edge.data("attributeId") !== undefined;
  });

  // Hide all expansion edges
  expansionEdges.forEach((edge) => {
    edge.hide();
  });

  // Process all connected nodes through expansion edges
  expansionEdges.forEach((edge) => {
    // Get the connected node (either source or target, whichever is not the current node)
    const connectedNode =
      edge.source().id() === node.id() ? edge.target() : edge.source();

    // Skip the original parent node
    if (connectedNode.id() === originalParent.id()) {
      return;
    }

    // Reset the expansion state of this connected node
    // This ensures that when the parent is re-expanded, this node will show "Expand" instead of "Collapse"
    if (connectedNode.data("isExpanded") === true) {
      connectedNode.data("isExpanded", false);
    }

    // Recursively process this connected node
    collapseNodeRecursively(
      cy,
      connectedNode,
      filterTemplates,
      originalParent,
      visitedNodes,
      processedEdges
    );

    // After processing all connections, check if this node should be hidden
    const remainingVisibleEdges = connectedNode
      .connectedEdges()
      .filter((e) => e.visible());

    // Only hide the node if it has no remaining visible connections
    if (remainingVisibleEdges.length === 0) {
      connectedNode.hide();

      // Update template filters if needed
      if (
        connectedNode.data("templateId") !== originalParent.data("templateId")
      ) {
        const templateId = connectedNode.data("templateId");
        const nodesWithTemplate = cy
          .nodes()
          .filter((n) => n.visible() && n.data("templateId") === templateId);

        if (nodesWithTemplate.length === 0) {
          const index = filterTemplates.findIndex(
            (item) => item.id == templateId
          );
          if (index !== -1) {
            filterTemplates.splice(index, 1);
          }
        }
      }
    }
  });

  // For the original node that was collapsed, update its state
  if (node === originalParent) {
    node.data("isExpanded", false);
  }
};

export const hidePointingRecursively = (
  cy,
  node,
  filterTemplates,
  originalParent,
  visitedNodes = new Set() // Track visited nodes
) => {
  // Check if the node is already visited to avoid infinite recursion
  if (visitedNodes.has(node.id())) {
    return;
  }

  // Mark the current node as visited
  visitedNodes.add(node.id());

  // Get all connected nodes (children)
  const children = node
    .neighborhood()
    .nodes()
    ?.filter((c) => c.id() !== originalParent.id());

  children.forEach((child) => {
    if (
      child.data("attributeId") === undefined ||
      originalParent.data("connectedNode") == child.id()
    ) {
      return; // Skip invalid or directly connected nodes
    }

    // Reset the expansion state of this child node if it was expanded
    if (child.data("isExpanded") === true) {
      child.data("isExpanded", false);
    }

    // Reset the children shown state if it was showing children
    if (child.data("isChildrensShown") === true) {
      child.data("isChildrensShown", false);
    }

    child.connectedEdges().forEach((edge) => {
      if (edge.data("target") == originalParent.id()) edge.hide();
    });
    // Hide the current child node
    // child.hide();

    // Handle template-related logic
    if (child.data("templateId") != originalParent.data("templateId")) {
      const templateId = child.data("templateId");
      const nodeWithTemplate = cy
        .nodes()
        .some((node) => node.data("templateId") === templateId);
      if (nodeWithTemplate) {
        const index = filterTemplates.findIndex(
          (item) => item.id == templateId
        );
        if (index !== -1) {
          filterTemplates.splice(index, 1);
        }
      }
    }

    // Recursively collapse children of this child
    hidePointingRecursively(
      cy,
      child,
      filterTemplates,
      originalParent,
      visitedNodes
    );
  });

  // Hide the current node if it's not the original parent
  if (node !== originalParent) {
    const visibleEdges = node.connectedEdges().filter((edge) => edge.visible());
    if (visibleEdges.empty()) {
      node.hide();
    }
  }
};
