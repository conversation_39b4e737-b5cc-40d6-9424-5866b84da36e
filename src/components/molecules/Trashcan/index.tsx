import { styled } from "@linaria/react";
import { getAttributeTitleWidth, useTheme } from "../../../utils";
import { useEffect, useState } from "react";
import { ResizableDiv } from "../ResizableDiv";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import { Breadcrumb, Empty, Skeleton, Tooltip } from "antd";
import { AttributeItem, SearchInputWithFilter } from "../../atoms";
import {
  DeleteNodePermanently,
  MoveTrashToSelected,
  RestoreNodeModal,
} from "../Modals";
import { useQuery, useQueryClient } from "react-query";
import { IAttributes, ITreeData } from "../../../interfaces";
import {
  GET_CHILDRENS,
  GET_NODE_ATTRIBUTES_DETAILS,
  TRASHCAN_PARENT_NODE_ID,
} from "../../../constants";
import { getAllNodes, getNodeDetails } from "../../../services/node";
import { useTranslation } from "react-i18next";
import { TrashcanTree } from "../TrashcanTree";
import { css } from "@linaria/core";
import {
  setSelectedTrash,
  setTrashCollapsed,
} from "../../../store/features/trashcan";
import { LoadingOutlined, MenuFoldOutlined } from "@ant-design/icons";
import { BottomNavigationDrawer } from "../../organisms";
import { useTrashcanActions } from "../../../utils/functions/customHooks";
import { useSearchParams } from "react-router-dom";
import {
  saveLocalSettings,
  getLocalSettingsDetails,
} from "../../../services/settings";

const STATE_KEY = "trashcan-sidebar";
const TREE_STATE_KEY = "tree-trashcan-sidebar";

const Trashcan = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();

  const { mask, attributeMask } = useSelector(
    (state: RootState) => state.sidebar
  );

  const { selectedTrash, performTrash, trashcanDrawerMask } = useSelector(
    (state: RootState) => state.trash
  );

  const bottomNavigationMask = useSelector(
    (state: RootState) => state.mask.bottomDrawer
  );
  const workingVersionMask = useSelector(
    (state: RootState) => state.mask.workingVersion
  );
  const authenticated = useSelector(
    (state: RootState) => state.auth.authenticated
  );

  const {
    generateTrashDataRecursively,
    generateSearchTrashDataRecursively,
    handleTrashNodeDelete,
  } = useTrashcanActions();

  const [attributesData, setAttributesData] = useState([]);
  const [sidebarWidth, setSidebarWidth] = useState(260);
  const [treeWidth, setTreeWidth] = useState(200);
  const [action, setAction] = useState({
    id: "",
    key: null,
    label: "",
  } as any);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [trashData, setTrashData] = useState([]);
  const [tempTrashData, setTempTrashData] = useState(null);

  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );

  const { isLoading, data, isFetching, isError } = useQuery<ITreeData[], Error>(
    [GET_CHILDRENS, TRASHCAN_PARENT_NODE_ID],
    () => getAllNodes(TRASHCAN_PARENT_NODE_ID),
    {
      onSuccess: (data) => {
        if (selectedTrash.keys.length === 0 && data.length > 0) {
          const selectedNode = data[0];
          dispatch(
            setSelectedTrash({
              keys: [selectedNode?.id],
              info: [
                {
                  id: selectedNode?.id,
                  isAsset: false,
                  name: selectedNode?.name,
                  parentId: selectedNode?.parentId,
                  templateId: selectedNode?.templateId,
                  isLeaf: selectedNode?.countChildren === 0,
                },
              ],
            })
          );
        }
      },
      enabled: !!authenticated && !!templatesData,
    }
  );

  const { isLoading: attributesLoading } = useQuery(
    [GET_NODE_ATTRIBUTES_DETAILS, selectedTrash?.keys[0]?.toString()],
    () => getNodeDetails(selectedTrash.keys[0]),
    {
      enabled: !!selectedTrash.keys[0],
      onSuccess: (data) => {
        const attributes = [];
        const selectedTemplateAttributes =
          templatesData[data.templateId]?.attributeTemplates || [];

        selectedTemplateAttributes?.forEach((attribute: IAttributes) => {
          const attributeValue = data?.body?.find(
            (item) => item.id == attribute.id
          );
          if (attributeValue) {
            attributes.push({
              ...attributeValue,
              ...attribute,
              value:
                attribute.type === "multiplicity"
                  ? {
                      text1: attributeValue?.value?.split("..")[0],
                      text2: attributeValue?.value?.split("..")[1],
                    }
                  : attribute.type === "switch"
                  ? attributeValue?.value || false
                  : attributeValue?.value,
            });
          }
        });

        setAttributesData(attributes);
      },
    }
  );

  useEffect(() => {
    // generating tree data
    const formattedTrashTreeData = generateTrashDataRecursively(data);
    setTrashData([...formattedTrashTreeData]);
  }, [data, isFetching]);

  useEffect(() => {
    const width = localStorage.getItem(STATE_KEY);
    if (width) setSidebarWidth(Number(width));

    const treeWidth = localStorage.getItem(TREE_STATE_KEY);
    if (treeWidth) setTreeWidth(Number(treeWidth));
  }, []);

  const isDeleteClicked = action?.key?.startsWith("delete");
  const isRestoreClicked = action?.key?.startsWith("restore");
  const isMoveToSelectedClicked = action?.key?.startsWith("move-to-selected");

  const [mainBreadcrumbHeight, setMainBreadcrumbHeight] = useState(0);

  useEffect(() => {
    if (performTrash) {
      const updatedTree = [...trashData];
      handleTrashNodeDelete(updatedTree);
      setTrashData(updatedTree);
      if (updatedTree.length === 0) {
        dispatch(setTrashCollapsed(true));
      }
    }
  }, [performTrash]);

  useEffect(() => {
    const mainBreadcrumbDiv = document.getElementById("mainBreadcrumb");

    if (mainBreadcrumbDiv) {
      const updateHeight = () => {
        const height = mainBreadcrumbDiv?.offsetHeight || 32;
        setMainBreadcrumbHeight(height);
      };

      updateHeight();
      const resizeObserver = new ResizeObserver(updateHeight);
      resizeObserver.observe(mainBreadcrumbDiv);

      return () => {
        resizeObserver.disconnect();
      };
    }
  }, [location?.pathname]);

  useEffect(() => {
    const attributeWrapper = document.getElementById("attributeWrapper");

    if (attributeWrapper) {
      const updateWidth = () => {
        const titles = document.querySelectorAll(
          ".trash-attribute-title"
        ) as any;
        titles.forEach((title) => {
          title.style.width = `fit-content`;
        });
        const maxTitleWidth = getAttributeTitleWidth(".trash-attribute-title");
        titles.forEach((title) => {
          title.style.width = `${maxTitleWidth}px`;
        });
      };

      updateWidth();
      const resizeObserver = new ResizeObserver(updateWidth);
      resizeObserver.observe(attributeWrapper);

      return () => {
        resizeObserver.disconnect();
      };
    }
  }, [attributesData, trashData?.length]);

  const trashcanCollapsed = useSelector(
    (state: RootState) => state.trash.trashCollapsed
  );
  const { data: localSettingsData } = useQuery(
    "localSettings",
    getLocalSettingsDetails,
    { staleTime: Infinity }
  );

  // Collapse handler
  const handleCollapse = () => {
    if (!localSettingsData) return;
    const prev = localSettingsData.body[0]?.value || {};
    const value = { ...prev, trashcanCollapsed: true };
    saveLocalSettings({ value });
    dispatch(setTrashCollapsed(true));
  };

  return (
    <ResizableDiv
      minWidth={130}
      resize="right"
      onResize={(_event, _direction, ref) => {
        if (ref.offsetWidth > 130) {
          setSidebarWidth(ref.offsetWidth);
        }
      }}
      maxWidth={"100%"}
      width={sidebarWidth}
      defaultWidth="260px"
      saveWidthToLocalStorage
      stateKey={STATE_KEY}
      className={resizeCSS}
      style={{ display: trashcanCollapsed ? "none" : undefined }}
    >
      {(mask ||
        attributeMask ||
        bottomNavigationMask ||
        workingVersionMask) && <Mask className="mask" />}

      {trashcanDrawerMask && <Mask className="trash-mask" />}

      <Wrapper theme={theme}>
        <TopBar
          theme={theme}
          style={{ height: mainBreadcrumbHeight || "34px" }}
        >
          <Breadcrumb
            separator=">"
            className="main-breadcrumbs"
            items={[
              {
                title: t("Trashcan"),
              },
            ]}
          />
          <Tooltip title={t("Collapse")}>
            <MenuFoldOutlined onClick={handleCollapse} />
          </Tooltip>
        </TopBar>
        <div className="container">
          {isLoading ? (
            <div style={{ minWidth: 250 }}>
              <Skeleton active paragraph={{ rows: 6 }} />
            </div>
          ) : (
            <>
              {isError ? (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={t("Error in fetching data!")}
                />
              ) : (
                <div className="tree-wrapper">
                  <ResizableDiv
                    minWidth={100}
                    resize="right"
                    onResize={(_event, _direction, ref) => {
                      if (ref.offsetWidth > 100) {
                        setTreeWidth(ref.offsetWidth);
                      }
                    }}
                    maxWidth={"100%"}
                    width={treeWidth}
                    defaultWidth="200px"
                    saveWidthToLocalStorage
                    stateKey={TREE_STATE_KEY}
                    className={treeResizeCSS}
                  >
                    <SearchInputWithFilter
                      parentId={TRASHCAN_PARENT_NODE_ID}
                      onSearch={(searchResponse, searchText) => {
                        if (!searchText && tempTrashData) {
                          setTrashData([...tempTrashData]);
                          setTempTrashData(null);
                          return;
                        }
                        if (!tempTrashData) setTempTrashData([...trashData]);
                        const searchTreeData =
                          generateSearchTrashDataRecursively(searchResponse);
                        setTrashData(searchTreeData);
                      }}
                    />
                    {trashData && trashData?.length === 0 ? (
                      <Empty
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                        description={t("No data")}
                      />
                    ) : (
                      <TrashcanTree
                        setAction={setAction}
                        dropdownOpen={dropdownOpen}
                        setDropdownOpen={setDropdownOpen}
                        trashData={trashData}
                        setTrashData={setTrashData}
                      />
                    )}
                  </ResizableDiv>
                  {trashData && trashData?.length === 0 ? (
                    <></>
                  ) : (
                    <div className="attributes-wrapper" id="attributeWrapper">
                      <div className="attributes">
                        <div>
                          {attributesLoading ? (
                            <LoadingOutlined />
                          ) : (
                            attributesData?.length === 0 && (
                              <Empty
                                image={Empty.PRESENTED_IMAGE_SIMPLE}
                                description={t("No attributes")}
                              />
                            )
                          )}
                          {!attributesLoading &&
                            attributesData?.map((item: any, index) => {
                              return (
                                <AttributeItem
                                  readOnly
                                  key={index}
                                  {...item}
                                  title={item.name}
                                  titleClassName="trash-attribute-title"
                                />
                              );
                            })}
                        </div>
                      </div>

                      <BottomNavigationDrawer
                        id={selectedTrash.keys[0]}
                        trashcan
                        fromModal
                        displaySaveCancel={trashcanDrawerMask}
                      />
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </Wrapper>

      {isDeleteClicked && (
        <DeleteNodePermanently
          isOpen={isDeleteClicked}
          afterDelete={() => {
            const updatedTree = [...trashData];
            handleTrashNodeDelete(updatedTree);
            setTrashData(updatedTree);
            if (updatedTree.length === 0) {
              dispatch(setTrashCollapsed(true));
            }
            if (searchParams.get("nodeId"))
              queryClient.invalidateQueries([
                GET_NODE_ATTRIBUTES_DETAILS,
                searchParams.get("nodeId"),
              ]);
          }}
          isMultiple={selectedTrash.keys.length > 1}
          action={action}
          onClose={() => {
            setDropdownOpen(false);
            setAction({ key: null });
          }}
        />
      )}

      {isMoveToSelectedClicked && (
        <MoveTrashToSelected
          isOpen={isMoveToSelectedClicked}
          action={action}
          setTrashData={setTrashData}
          trashData={trashData}
          isMultiple={selectedTrash.keys.length > 1}
          onClose={() => {
            setDropdownOpen(false);
            setAction({ key: null });
          }}
        />
      )}

      {isRestoreClicked && (
        <RestoreNodeModal
          isOpen={isRestoreClicked}
          setTrashData={setTrashData}
          trashData={trashData}
          action={action}
          isMultiple={selectedTrash.keys.length > 1}
          onClose={() => {
            setDropdownOpen(false);
            setAction({ key: null });
          }}
        />
      )}
    </ResizableDiv>
  );
};

export { Trashcan };

const Mask = styled.div`
  height: 100%;
  width: 100%;
  z-index: 100;
  top: 0px;
  left: 0px;
`;

const treeResizeCSS = css`
  flex: unset !important;
  border-right: 1px solid #eee;
  display: flex;
  flex-direction: column;
`;

const resizeCSS = css`
  flex: unset !important;
  background-color: #fff !important;

  & > .indicator {
    top: 48% !important;
  }
`;

const TopBar = styled.div<{ theme: any }>`
  display: flex;
  gap: 10px;
  background-color: ${({ theme }) => theme.trashBreadcrumbsColor};
  padding-right: 10px;
  align-items: center;

  & button {
    font-size: 13px;
    height: 24px;
    padding: 0px 10px;
    border-radius: 3px;
    z-index: 10000;
  }

  & > span {
    color: ${({ theme }) => theme.trashBreadcrumbsFontColor};
    font-size: 16px;
    cursor: pointer;
  }
  & .ant-breadcrumb {
    flex: 1;
    color: #fff;
    min-height: 34px;
    padding: 0px 20px;
    display: flex;
    align-items: center;

    & .ant-dropdown-trigger a,
    .ant-breadcrumb-separator {
      color: ${({ theme }) => theme.trashBreadcrumbsFontColor};
    }
  }

  & ol > li:last-child .ant-breadcrumb-link {
    color: #fff100 !important;
    font-weight: 600;
  }
  & .ant-breadcrumb-link:hover {
    opacity: 0.8;
  }
`;

const Wrapper = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  border-right: 1px solid #efefef;
  height: 100%;

  & .ant-tree-title {
    width: 100%;
  }
  & .ant-tree-switcher > svg {
    width: 20px;
    transition: all 0.2s ease-in;
    height: fit-content;

    & path {
      fill: ${({ theme }) => theme.colorPrimary};
    }
  }

  & .attributes-wrapper {
    position: relative;
    flex: 1;
    overflow: auto;
    min-width: 90px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-wrap: nowrap;

    & .attributes {
      overflow: auto;
      width: 100%;
      & > div {
        padding: 12px;
      }
    }
    & .drawer {
      width: 100%;
    }

    & .anticon-loading {
      font-size: 24px;
      display: flex;
      justify-content: center;
    }
  }

  & .tree {
    padding: 0px 12px 12px 12px;
  }
  & .tree-wrapper {
    display: flex;
    height: 100%;
  }

  & .container {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: auto;

    & .search-wrapper {
      padding: 12px;
    }
  }
`;
