import {
  CaretRightFilled,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  RetweetOutlined,
  RollbackOutlined,
} from "@ant-design/icons";
import type { MenuProps } from "antd";
import { Trans } from "react-i18next";
import { useQueryClient } from "react-query";
import { GET_ACTIONS_LIST, GET_ALL_TEMPLATES_KEY } from "../queryKeys";
import {
  DELETED_FLAG,
  EDIT_MODE_FLAG,
  IActions,
  INodeFlags,
  ITemplates,
} from "../../interfaces";
import {
  DISABLED_METAMODEL_NODE_ID,
  OBJECT_FOLDER_TEMPLATE_ID,
  OBJECT_TEMPLATE_ID,
  TEMPLATES_ATTRIBUTE_TEMPLATE_ID,
  TEMP_GROUPING_NODE_ID,
} from "..";

export type IActionMenus =
  | "rename"
  | "share"
  | "like"
  | "unlike"
  | "cut"
  | "add-system-group"
  | "add-document-group"
  | "export"
  | "print"
  | "vizualization"
  | "delete"
  | "add-asset"
  | "update-asset";

export const getWorkingVersionMenus = (
  templateId: number,
  permissions: string[]
) => {
  const COMMON_MENUS = [
    permissions?.includes("EDIT") && {
      label: <Trans>Rename</Trans>,
      icon: <EditOutlined />,
      key: "rename",
    },
    templateId == TEMPLATES_ATTRIBUTE_TEMPLATE_ID &&
    permissions.includes("DELETE")
      ? {
          label: <Trans>Delete</Trans>,
          key: "delete",
          icon: <DeleteOutlined />,
        }
      : null,
  ];

  const templatesMenus = [];
  const queryClient = useQueryClient();
  const allTemplates = queryClient.getQueryData(
    GET_ALL_TEMPLATES_KEY
  ) as ITemplates[];

  const allActions = queryClient.getQueryData(GET_ACTIONS_LIST) as IActions[];

  if (templateId && permissions.includes("ADD")) {
    const selectedTemplate = allTemplates?.filter(
      (item: ITemplates) => item.id == templateId
    );

    selectedTemplate?.forEach((template) => {
      if (template.allowedChildren) {
        template.allowedChildren.forEach((allowedChild) =>
          templatesMenus.push({
            label: (
              <>
                <Trans>Add</Trans> {<Trans>{allowedChild?.name}</Trans>}
              </>
            ),
            icon: <PlusOutlined />,
            key: `add-with-template-${allowedChild?.id}`,
            templateid: allowedChild?.id,
          })
        );
      }

      if (template?.actions?.length > 0) {
        template.actions.forEach((actionId: number) => {
          const selectedAction = allActions.find(
            (action) => action.id === actionId
          );
          if (selectedAction) {
            templatesMenus.push({
              label: (
                <>
                  <Trans>{selectedAction.name}</Trans>
                </>
              ),
              icon: <CaretRightFilled />,
              key: `action-${actionId}`,
              actionid: actionId,
            });
          }
        });
      }
    });
  }

  if (templateId) {
    return [...templatesMenus, ...COMMON_MENUS];
  }
  return [...COMMON_MENUS];
};

// const COMMON_MENUS = [
//   {
//     key: "share",
//     label: (
//       <>
//         <Trans>Share</Trans> <Trans>(mockup)</Trans>
//       </>
//     ),
//     icon: <ShareAltOutlined />,
//   },
//   {
//     key: "like",
//     label: (
//       <span>
//         <Trans count={0}>useful</Trans> <Trans>(mockup)</Trans>
//       </span>
//     ),
//     icon: <LikeOutlined />,
//   },
//   {
//     key: "unlike",
//     label: (
//       <span>
//         <Trans count={0}>not useful</Trans> <Trans>(mockup)</Trans>
//       </span>
//     ),
//     icon: <DislikeOutlined />,
//   },
//   {
//     label: (
//       <>
//         <Trans>Cut</Trans> <Trans>(mockup)</Trans>
//       </>
//     ),
//     icon: <ScissorOutlined />,
//     key: "cut",
//   },

//   {
//     label: (
//       <>
//         <Trans>Print</Trans> <Trans>(mockup)</Trans>
//       </>
//     ),
//     key: "print",
//     icon: <PrinterOutlined />,
//   },
// ];

const hasAllDisabledTemplates = (selected) => {
  const disabledTemplates = selected?.filter((node) =>
    node.flag?.includes(DELETED_FLAG)
  );
  return disabledTemplates.length === selected.length;
};

const getSortOptions = (t) => {
  return [
    {
      key: "sort-asc",
      label: t("Sort alphabetically ascending"),
      icon: <i className="pi pi-sort-alpha-up" />,
    },
    {
      key: "sort-desc",
      label: t("Sort alphabetically descending"),
      icon: <i className="pi pi-sort-alpha-down" />,
    },
    {
      key: "sort-visual-order",
      icon: <i className="pi pi-sort-numeric-up" />,
      label: t("Sort by visual order"),
    },
  ];
};

export const getSidebarMenus = (
  templateId: number,
  status: INodeFlags[],
  metamodel,
  id,
  isWorkingVersionActive: boolean,
  isLeaf: boolean,
  selected,
  body: any[],
  PERMISSIONS: string[],
  t
) => {
  const isMultiple = selected?.keys?.length > 1;

  if (isMultiple) {
    return [
      {
        label: (
          <Trans>
            {metamodel
              ? hasAllDisabledTemplates(selected?.info)
                ? "Delete permanently"
                : "Disable selected"
              : "Move selected to trash"}
          </Trans>
        ),
        key: "delete-all",
        icon: <DeleteOutlined />,
      },
    ];
  }

  const IS_SYSTEM_TEMPLATES = id < 0;

  const menus = [];
  if (
    Number(templateId) === OBJECT_TEMPLATE_ID &&
    !status?.includes(DELETED_FLAG) &&
    PERMISSIONS?.includes("EDIT")
    // !IS_SYSTEM_TEMPLATES
  ) {
    if (status?.includes(EDIT_MODE_FLAG)) {
      if (isWorkingVersionActive) {
        menus.push({
          label: <Trans>Close draft version</Trans>,
          key: "close-working-version",
          icon: <EditOutlined />,
        });
      } else {
        menus.push({
          label: <Trans>Go to draft version</Trans>,
          key: "go-to-working-version",
          icon: <EditOutlined />,
        });
      }
    } else {
      menus.push({
        label: <Trans>Create a draft version</Trans>,
        key: "create-working-version",
        icon: <PlusOutlined />,
      });
    }
  }

  if (
    Number(templateId) !== OBJECT_TEMPLATE_ID &&
    Number(templateId) !== TEMPLATES_ATTRIBUTE_TEMPLATE_ID &&
    !status?.includes(DELETED_FLAG) &&
    id !== DISABLED_METAMODEL_NODE_ID &&
    PERMISSIONS?.includes("EDIT")
    // !IS_SYSTEM_TEMPLATES
  ) {
    menus.push({
      label: (
        <Trans>
          {Number(templateId) === TEMP_GROUPING_NODE_ID ? "Edit" : "Rename"}
        </Trans>
      ),
      icon: <EditOutlined />,
      key: "rename",
    });
  }

  // "Sort" menu is shown ONLY if user has "SORT" permission and node is not a leaf.
  // It is NOT gated by any flags (e.g. EDIT_MODE_FLAG, DELETED_FLAG).
  if (PERMISSIONS?.includes("SORT") && !isLeaf) {
    menus.push({ type: "divider" });
    menus.push({
      label: <Trans>{"Sort"}</Trans>,
      icon: <i className="pi pi-sort-alt" />,
      key: "sort",
      children: getSortOptions(t),
    });
  }

  // if (PERMISSIONS?.includes("PERMISSION") && !IS_SYSTEM_TEMPLATES && isModel) {
  //   menus.push({ type: "divider" });
  //   menus.push({
  //     label: <Trans>{"Set Permissions"}</Trans>,
  //     icon: <UserOutlined />,
  //     key: "set-permissions",
  //   });
  //   menus.push({ type: "divider" });
  // }

  if (!IS_SYSTEM_TEMPLATES) {
    if (Number(templateId) !== TEMPLATES_ATTRIBUTE_TEMPLATE_ID) {
      if (metamodel) {
        if (status?.includes(DELETED_FLAG)) {
          menus.push({
            label: <Trans>Activate</Trans>,
            key: "restore-template",
            icon: <DeleteOutlined />,
          });
          if (PERMISSIONS?.includes("DELETE")) {
            menus.push({
              label: <Trans>Delete permanently</Trans>,
              key: "delete-template-permanently",
              icon: <DeleteOutlined />,
            });
          }
        } else {
          if (
            !(templateId == OBJECT_FOLDER_TEMPLATE_ID && !isLeaf) &&
            PERMISSIONS?.includes("DELETE")
          ) {
            menus.push({
              label:
                templateId == OBJECT_FOLDER_TEMPLATE_ID ? (
                  <Trans>Delete</Trans>
                ) : (
                  <Trans>Disable</Trans>
                ),
              key: "delete",
              icon: <DeleteOutlined />,
            });
          }
        }
      } else {
        if (PERMISSIONS?.includes("TRASH"))
          menus.push({
            label: <Trans>Move to Trashcan</Trans>,
            key: "delete",
            icon: <DeleteOutlined />,
          });
      }
    }
  }

  // if (!metamodel) {
  //   menus.push({
  //     label: (
  //       <>
  //         <Trans>Export</Trans> <Trans>(mockup)</Trans>
  //       </>
  //     ),
  //     key: "export",
  //     icon: <ExportOutlined />,
  //   });
  // }

  // {
  //   label: "Vizualization",
  //   key: "vizualization",
  //   icon: <LineChartOutlined />,
  // },

  const templatesMenus = [];
  const queryClient = useQueryClient();
  const allTemplates = queryClient.getQueryData(
    GET_ALL_TEMPLATES_KEY
  ) as ITemplates[];

  if (templateId == TEMP_GROUPING_NODE_ID && PERMISSIONS?.includes("ADD")) {
    const allowedChildren = body?.find(
      (node) => node.type === "allowedChildren"
    )?.value;

    if (allowedChildren) {
      if (allowedChildren?.length < 2) {
        allowedChildren?.forEach((allowedChild) => {
          if (!allowedChild?.inTrash) {
            templatesMenus.push({
              label: (
                <>
                  <Trans>Add</Trans> {<Trans>{allowedChild?.name} </Trans>}
                </>
              ),
              icon: <PlusOutlined />,
              key: `add-with-template-${allowedChild?.id}`,
              templateid: allowedChild?.id,
            });
          }
        });
      } else {
        const childMenus = [];
        const sortedAllowedChildrens = [...allowedChildren].sort((a, b) =>
          a.name.localeCompare(b.name)
        );

        sortedAllowedChildrens?.forEach((allowedChild) => {
          if (!allowedChild?.inTrash) {
            childMenus.push({
              label: (
                <>
                  <Trans>Add</Trans> {<Trans>{allowedChild?.name} </Trans>}
                </>
              ),
              icon: <PlusOutlined />,
              key: `add-with-template-${allowedChild?.id}`,
              templateid: allowedChild?.id,
            });
          }
        });

        templatesMenus.push({
          label: (
            <>
              <Trans>Add</Trans>
            </>
          ),
          icon: <PlusOutlined />,
          children: childMenus,
          key: `add-with-template`,
        });
      }
    }
  }

  const allActions = queryClient.getQueryData(GET_ACTIONS_LIST) as IActions[];

  if (
    id === -100 ||
    (templateId &&
      Number(templateId) !== OBJECT_TEMPLATE_ID &&
      !status?.includes(DELETED_FLAG) &&
      id !== DISABLED_METAMODEL_NODE_ID &&
      PERMISSIONS?.includes("ADD"))
    // !IS_SYSTEM_TEMPLATES)
  ) {
    const selectedTemplate = allTemplates?.filter(
      (item: ITemplates) => item.id == templateId
    );

    selectedTemplate?.forEach((template) => {
      if (template.allowedChildren) {
        if (template.allowedChildren?.length < 2) {
          template.allowedChildren?.forEach((allowedChild) => {
            if (!allowedChild?.inTrash) {
              templatesMenus.push({
                label: (
                  <>
                    <Trans>Add</Trans> {<Trans>{allowedChild?.name}</Trans>}
                  </>
                ),
                icon: <PlusOutlined />,
                key: `add-with-template-${allowedChild?.id}`,
                templateid: allowedChild?.id,
              });
            }
          });
        } else {
          const sortedAllowedChildrens = [...template.allowedChildren].sort(
            (a, b) => a.name.localeCompare(b.name)
          );

          const childMenus = [];
          sortedAllowedChildrens?.forEach((allowedChild) => {
            if (!allowedChild?.inTrash) {
              childMenus.push({
                label: <>{<Trans>{allowedChild?.name}</Trans>}</>,
                icon: <PlusOutlined />,
                key: `add-with-template-${allowedChild?.id}`,
                templateid: allowedChild?.id,
              });
            }
          });
          templatesMenus.push({
            label: (
              <>
                <Trans>Add</Trans>
              </>
            ),
            icon: <PlusOutlined />,
            children: childMenus,
            key: `add-with-template`,
          });
        }
      }

      if (template?.actions?.length > 0 && PERMISSIONS?.includes("RUN")) {
        template.actions.forEach((actionId: number) => {
          const selectedAction = allActions.find(
            (action) => action.id === actionId
          );
          if (selectedAction) {
            templatesMenus.push({
              label: (
                <>
                  <Trans>{selectedAction.name}</Trans>
                </>
              ),
              icon: <CaretRightFilled />,
              key: `action-${actionId}`,
              actionid: actionId,
            });
          }
        });
      }
    });
  }

  // menus.push({ type: "divider" });
  // menus.push({
  //   label: <Trans>Subscribe</Trans>,
  //   key: "subscribe",
  //   icon: <MailOutlined />,
  // });

  if (templateId) {
    return [...templatesMenus, ...menus];
  }
  return [...menus];
};

export const getTrashcanMenus = (
  displayMoveToSelected: boolean,
  parentExists: boolean,
  isMultiple: boolean,
  PERMISSIONS: string[]
) => {
  if (isMultiple) {
    return [
      PERMISSIONS.includes("DELETE")
        ? {
            label: <Trans>Delete selected permanently</Trans>,
            key: "delete-all",
            icon: <DeleteOutlined />,
          }
        : null,
      displayMoveToSelected &&
        PERMISSIONS.includes("TRASH") && {
          label: <Trans>Restore to selected</Trans>,
          key: "move-to-selected",
          icon: <RollbackOutlined />,
        },
    ];
  }

  return [
    PERMISSIONS.includes("DELETE") && {
      label: <Trans>Delete permanently</Trans>,
      icon: <DeleteOutlined />,
      key: "delete",
    },
    parentExists && PERMISSIONS.includes("TRASH")
      ? {
          label: <Trans>Restore to original position</Trans>,
          icon: <RetweetOutlined />,
          key: "restore",
        }
      : null,
    displayMoveToSelected && PERMISSIONS.includes("TRASH")
      ? {
          label: <Trans>Restore to selected</Trans>,
          icon: <RollbackOutlined />,
          key: "move-to-selected",
        }
      : null,
  ];
};

export const SIDEBAR_MENUS_MULTIPLE: MenuProps["items"] = [
  {
    label: <Trans>Move selected to trash</Trans>,
    key: "delete-all",
    icon: <DeleteOutlined />,
  },
];
