import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import linaria from "@linaria/vite";
import svgr from "vite-plugin-svgr";
import comlink from 'vite-plugin-comlink';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  const isProd = mode === 'production';

  return {
    plugins: [
      linaria({
        include: ["src/**/*.{ts,tsx}"],
        exclude: [
          /node_modules/,
          /\.vite/,
          /deps/,
          /@linaria/,
          /chartjs-plugin-zoom/,
          /dayjs/
        ],
        sourceMap: !isProd,
        babelOptions: {
          presets: ["@babel/preset-typescript", "@babel/preset-react", "@linaria"],
        },
      }),
      react(),
      comlink(),
      svgr(),
    ],
    base: env.VITE_APP_BASE_URL || '/',
    worker: {
      format: 'es',
    },
    // Removed optimizeDeps to let Vite auto-optimize dependencies
    build: {
      sourcemap: !isProd,
      target: 'es2020', // Modern browsers target for better optimization
      chunkSizeWarningLimit: 1000,
      emptyOutDir: isProd,
      rollupOptions: {
        onwarn(warning, warn) {
          // Suppress pure annotation warnings
          if (warning.code === 'INVALID_ANNOTATION' && warning.message.includes('/*#__PURE__*/')) {
            return;
          }
          warn(warning);
        },
      }
    },
  };
});
