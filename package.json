{"name": "cdo-front", "private": true, "version": "0.0.0", "type": "module", "homepage": ".", "scripts": {"dev": "vite  --port 3006", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint --max-warnings=0 src", "prepare": "husky install", "storybook": "storybook dev -p 6006", "storybook-docs": "storybook dev --docs", "build-storybook": "storybook build", "serve:dist": "node serve-dist.cjs"}, "lint-staged": {"src/**/*.{js,json,ts,tsx}": ["yarn lint --fix --quiet"]}, "dependencies": {"@ant-design/icons": "^4.8.0", "@babel/preset-react": "^7.18.6", "@dnd-kit/core": "^6.0.8", "@dnd-kit/sortable": "^7.0.2", "@hookform/resolvers": "^2.9.10", "@linaria/babel-preset": "^4.4.3", "@linaria/core": "^4.2.2", "@linaria/react": "^4.3.0", "@linaria/rollup": "^4.2.2", "@linaria/webpack-loader": "^4.1.15", "@reduxjs/toolkit": "^1.9.7", "@sentry/react": "^7.86.0", "@sentry/tracing": "^7.86.0", "@tinymce/tinymce-react": "^5.0.1", "@types/react-cytoscapejs": "^1.2.2", "ag-grid-community": "^33.1.1", "ag-grid-enterprise": "^33.0.3", "ag-grid-react": "^33.1.1", "antd": "^5.19.0", "apexcharts": "^3.36.3", "axios": "^1.2.1", "chart.js": "^4.4.7", "chartjs-plugin-zoom": "^2.2.0", "comlink": "^4.4.2", "cron-expression-validator": "^1.0.20", "cronstrue": "^2.51.0", "cytoscape": "^3.26.0", "cytoscape-context-menus": "^4.1.0", "cytoscape-cose-bilkent": "^4.1.0", "cytoscape-cxtmenu": "^3.5.0", "cytoscape-expand-collapse": "^4.1.0", "cytoscape-fcose": "^2.2.0", "date-fns": "^2.29.3", "dayjs": "^1.11.11", "dayjs-plugin-utc": "^0.1.2", "file-saver": "^2.0.5", "i18next": "^22.0.6", "i18next-browser-languagedetector": "^7.0.1", "lodash": "^4.17.21", "lodash.isequal": "^4.5.0", "primeicons": "^6.0.1", "primereact": "^10.5.1", "prismjs": "^1.30.0", "re-resizable": "^6.9.9", "react": "^18.2.0", "react-color": "^2.19.3", "react-cron-generator": "^2.0.13", "react-cytoscapejs": "^2.0.0", "react-dom": "^18.2.0", "react-drag-listview": "^2.0.0", "react-draggable": "^4.4.5", "react-gesture-responder": "^2.1.0", "react-grid-dnd": "^2.1.2", "react-helmet": "^6.1.0", "react-highlight-words": "^0.20.0", "react-hook-form": "^7.40.0", "react-i18next": "^12.0.0", "react-query": "^3.39.2", "react-redux": "^8.1.3", "react-resizable": "^3.0.5", "react-router-dom": "^6.4.4", "react-simple-code-editor": "^0.14.1", "react-waypoint": "^10.3.0", "sql-highlight": "^6.0.0", "universal-cookie": "^4.0.4", "vite-plugin-svgr": "^2.2.2", "xlsx": "^0.18.5", "yup": "^0.32.11"}, "devDependencies": {"@babel/core": "^7.20.12", "@babel/preset-typescript": "^7.21.0", "@rollup/plugin-node-resolve": "^15.0.1", "@storybook/addon-docs": "^7.0.18", "@storybook/addon-essentials": "^7.0.18", "@storybook/addon-interactions": "^6.5.16", "@storybook/addon-links": "^7.0.18", "@storybook/blocks": "^7.0.18", "@storybook/builder-vite": "^7.0.18", "@storybook/react": "^7.0.18", "@storybook/react-vite": "^7.0.18", "@storybook/testing-library": "^0.0.13", "@types/react": "^18.0.24", "@types/react-dom": "^18.0.8", "@typescript-eslint/parser": "^5.45.0", "@vitejs/plugin-react": "^4.4.1", "babel-loader": "^8.3.0", "eslint": "^8.28.0", "eslint-config-prettier": "^8.5.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-storybook": "^0.6.10", "husky": "^8.0.2", "lint-staged": "^13.1.0", "prettier": "^2.8.0", "prop-types": "^15.8.1", "storybook": "^7.0.18", "storybook-addon-react-router-v6": "^1.0.1", "storybook-react-context": "^0.6.0", "typescript": "^4.6.4", "vite": "^5.2.0", "vite-plugin-comlink": "^5.1.0", "@linaria/vite": "^4.2.2"}}