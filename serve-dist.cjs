// serve-dist.cjs
// Node.js script to build with yarn and serve the dist directory as static files
// Uses the VITE_APP_BASE_URL from .env.dev for correct base path
// Logs each step with emoji and timestamp, provides help, truncates build output, shows a loader spinner, and distinguishes warnings from errors

const { exec } = require('child_process');
const express = require('express');
const path = require('path');
const fs = require('fs');
const open = require('open');

const DIST_DIR = path.join(__dirname, 'dist');
const ENV_FILE = '.env.dev';
const ENV_PATH = path.join(__dirname, ENV_FILE);
const PORT = 5000;

// Helper to get current timestamp
function now() {
  return new Date().toLocaleTimeString();
}

// Helper to log steps with emoji and timestamp
function logStep(emoji, message) {
  console.log(`[${now()}] ${emoji}  ${message}`);
}

// Loader spinner
function createSpinner(message) {
  const frames = ['⠋','⠙','⠹','⠸','⠼','⠴','⠦','⠧','⠇','⠏'];
  let i = 0;
  let interval;
  function start() {
    process.stdout.write('\x1B[?25l'); // hide cursor
    interval = setInterval(() => {
      process.stdout.write(`\r${frames[i = ++i % frames.length]} ${message} `);
    }, 80);
  }
  function stop(finalMsg) {
    clearInterval(interval);
    process.stdout.write('\r'); // clear line
    process.stdout.write('\x1B[?25h'); // show cursor
    if (finalMsg) console.log(finalMsg);
  }
  return { start, stop };
}

// Helper to extract VITE_APP_BASE_URL from .env.dev
function getBasePath() {
  if (fs.existsSync(ENV_PATH)) {
    const envContent = fs.readFileSync(ENV_PATH, 'utf8');
    const match = envContent.match(/^VITE_APP_BASE_URL\s*=\s*(.*)$/m);
    if (match && match[1]) {
      let base = match[1].trim();
      // Ensure base path starts and ends with /
      if (!base.startsWith('/')) base = '/' + base;
      if (!base.endsWith('/')) base = base + '/';
      return base;
    }
  }
  // Default to root if not found
  return '/';
}

// Print help message
function printHelp(basePath) {
  console.log('\n==============================');
  console.log('🛠️  Static Build Server Help');
  console.log('------------------------------');
  console.log(`• Env file used: ${ENV_FILE}`);
  console.log(`• Base path: ${basePath}`);
  console.log(`• Output dir: dist/`);
  console.log(`• Server port: ${PORT}`);
  console.log('• To stop the server: Press Ctrl+C');
  console.log('• What this does:');
  console.log('    1. Builds the app with production optimizations using .env.dev');
  console.log('    2. Serves the compiled dist/ at the correct base path with SPA routing');
  console.log('    3. Opens your browser to the correct URL');
  console.log('==============================\n');
}

// Truncate and print build output
function printTruncatedOutput(output, maxHead = 10, maxTail = 10) {
  const lines = output.split('\n');
  if (lines.length <= maxHead + maxTail) {
    console.log(output);
    return;
  }
  const head = lines.slice(0, maxHead);
  const tail = lines.slice(-maxTail);
  console.log(head.join('\n'));
  console.log(`...truncated ${lines.length - maxHead - maxTail} lines...`);
  console.log(tail.join('\n'));
}

// Step 1: Build the project
const basePath = getBasePath();
printHelp(basePath);

logStep('🔍', `Using env file: ${ENV_FILE}`);
logStep('📦', `Detected base path: ${basePath}`);
logStep('🏗️', 'Starting build: yarn build --mode dev');

const spinner = createSpinner('Building (this may take a while)...');
spinner.start();

const buildProcess = exec('yarn build --mode dev', { stdio: 'pipe' });

let buildStdout = '';
let buildStderr = '';

buildProcess.stdout.on('data', (data) => {
  buildStdout += data.toString();
});
buildProcess.stderr.on('data', (data) => {
  buildStderr += data.toString();
});

buildProcess.on('exit', (code) => {
  spinner.stop();
  if (buildStdout) {
    logStep('📝', 'Build output:');
    printTruncatedOutput(buildStdout);
  }
  if (buildStderr) {
    if (code === 0) {
      logStep('⚠️', 'Build warnings:');
      console.warn(buildStderr);
    } else {
      logStep('❌', 'Build errors:');
      console.error(buildStderr);
    }
  }
  if (code !== 0) {
    logStep('❌', `Build failed with exit code ${code}`);
    process.exit(code);
  }
  logStep('✅', 'Build completed successfully.');

  // Step 2: Serve the dist directory at the base path
  const app = express();
  logStep('🚀', `Serving static files at ${basePath} from dist/`);
  app.use(basePath, express.static(DIST_DIR));

  // SPA routing: all non-file requests under basePath/* return index.html
  app.get(basePath + '*', (req, res) => {
    res.sendFile(path.join(DIST_DIR, 'index.html'));
  });

  app.listen(PORT, () => {
    const url = `http://localhost:${PORT}${basePath}`;
    logStep('🌐', `Server running at ${url}`);
    logStep('🖥️', 'Opening browser...');
    open(url);
    logStep('ℹ️', 'To stop the server, press Ctrl+C');
  });
});